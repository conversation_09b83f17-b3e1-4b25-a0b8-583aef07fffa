{"name": "router-control", "version": "1.0.0", "description": "Video Router Control Application", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder --win", "package-win": "electron-builder --win --x64", "package-portable": "electron-builder --win portable --x64"}, "dependencies": {"express": "^4.18.2", "ws": "^8.13.0"}, "devDependencies": {"electron": "^29.0.0", "electron-builder": "^24.9.1", "electron-icon-builder": "^2.0.1", "electron-icon-maker": "^0.0.5", "png-to-ico": "^2.1.8"}, "build": {"appId": "com.router.control", "productName": "Router Control", "directories": {"output": "dist"}, "files": ["**/*", "!dist/", "!node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "win": {"target": ["nsis", "portable"]}, "portable": {"artifactName": "RouterControlPortable.exe"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}
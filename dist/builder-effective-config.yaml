directories:
  output: dist
  buildResources: build
appId: com.router.control
productName: Router Control
files:
  - filter:
      - '**/*'
      - '!dist/'
      - '!node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}'
      - '!node_modules/*/{test,__tests__,tests,powered-test,example,examples}'
      - '!node_modules/.bin'
      - '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}'
      - '!.editorconfig'
      - '!**/._*'
      - '!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}'
      - '!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}'
      - '!**/{appveyor.yml,.travis.yml,circle.yml}'
      - '!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}'
win:
  target:
    - nsis
    - portable
portable:
  artifactName: RouterControlPortable.exe
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
electronVersion: 29.4.6

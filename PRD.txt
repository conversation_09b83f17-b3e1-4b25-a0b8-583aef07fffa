<PERSON><PERSON><PERSON><PERSON>, zde je kompletní finální verze Produktového Požadavkového Dokumentu (PRD) pro Copilot agenta, kter<PERSON> zahrnuje všechna vaše upřesnění.

Product Requirements Document (PRD): Video Router Web Control Interface (Verze 1.2)

1. Úvod / Přehled

Cíl: Vytvořit responzivní webovou aplikaci, kter<PERSON> umožní uživatelům konfigurovat a ovládat video router Evertz <PERSON>ua<PERSON>z (nebo kompatibilní) pomocí protokolu Quartz RCP-T01-1v6 přes TCP/IP spojení. Aplikace bude ovládat pouze video úroveň ('V').
Prostředí: Aplikace poběží v moderním webovém prohlížeči. Bude vyžadovat backendovou komponentu (např. Node.js server, Python server) pro zajištění TCP komunikace, protože prohlížeče nemohou přímo navazovat TCP spojení. Copilot by měl být schopen vygenerovat jak frontend (HTML, CSS, JavaScript), tak základní backend pro TCP komunikaci.
2. Cíle Projektu

Umožnit snadnou konfiguraci připojení k video routeru.
Poskytnout intuitivní způsob organizace vstupů a výstupů routeru do logických skupin a přiřazení přátelských názvů fyzickým portům.
Umožnit uživatelům vytvořit si vlastní ovládací rozhraní pomocí mřížek tlačítek (grids).
Poskytnout jednoduché a spolehlivé rozhraní pro provádění přepínání (routing) signálů na video úrovni.
Zajistit responzivní design pro použití na různých velikostech obrazovek.
Zobrazovat v reálném čase aktuální stav routeru (alespoň pro nakonfigurované destinace).
Poskytnout logovací okno pro sledování komunikace a stavu.
3. Uživatelé a Scénáře (User Stories)

Jako operátor chci nastavit IP adresu a port routeru, abych se k němu mohl připojit.
Jako operátor chci vytvářet kategorie (např. "Kamery", "Servery") a subkategorie (např. "HD Kamery", "Grafika"), abych mohl logicky třídit vstupy a výstupy.
Jako operátor chci přiřadit fyzické číslo vstupu/výstupu routeru (např. Vstup 1, Výstup 5) ke kategorii/subkategorii a dát mu přátelský název (např. "CAM1", "PGM OUT"), abych je snadno identifikoval.
Jako operátor chci vytvořit jednu nebo více mřížek (grids) s tlačítky, abych si mohl uspořádat často používané vstupy a výstupy.
Jako operátor chci přiřadit nakonfigurované vstupy a výstupy (nebo celé kategorie/subkategorie) k tlačítkům v mých mřížkách.
Jako operátor chci vidět na hlavní obrazovce mřížku se vstupy (Sources) a mřížku s výstupy (Destinations), abych mohl provádět přepínání.
Jako operátor chci kliknout na tlačítko destinace, poté kliknout na tlačítko vstupu a následně stisknout tlačítko "TAKE", abych odeslal příkaz k přepnutí video signálu do routeru.
Jako operátor chci, aby webové rozhraní zobrazovalo aktuální stav přepnutí pro destinace zobrazené v mřížce, i když byla změna provedena z jiného ovládacího panelu (real-time aktualizace).
Jako operátor chci mít k dispozici okno s logy, abych viděl stav připojení, odeslané příkazy, přijaté odpovědi a případné chyby.
4. Funkční Požadavky

4.1. Konfigurace (Configuration Section)

4.1.1. Nastavení Připojení:
Pole pro zadání IP adresy routeru (výchozí: *************).
Pole pro zadání TCP portu routeru (výchozí: 4000).
Tlačítko pro uložení nastavení připojení.
Indikátor stavu připojení (Připojeno / Odpojeno / Chyba).
4.1.2. Správa Kategorií a Subkategorií:
Možnost vytvářet, přejmenovávat a mazat kategorie pro vstupy (Sources) a výstupy (Destinations).
Možnost vytvářet, přejmenovávat a mazat subkategorie v rámci kategorií.
4.1.3. Správa Vstupů a Výstupů:
Možnost přidávat fyzické vstupy/výstupy routeru do konfigurace aplikace.
Pro každý vstup/výstup:
Zadat jeho číslo dle protokolu Quartz (1 až max. velikost routeru, např. 384) [zdroj: 106]. Toto číslo se použije v TCP příkazech ({srce} nebo {dest}). Předpokládá se, že toto číslo přímo odpovídá fyzickému označení portu na routeru (např. číslo 1 odpovídá portu "Input 1" nebo "Output 1").
Zadat uživatelsky přátelský název (např. "CAM1"). Tento název se zobrazí v UI na tlačítkách.
Přiřadit ho do existující kategorie/subkategorie (nebo nechat bez kategorie).
Možnost upravovat a mazat nakonfigurované vstupy/výstupy.
Aplikace musí být schopna pracovat s routery o velikosti až 384x384 (tj. povolit zadávání čísel v rozsahu 1-384).
4.1.4. Správa Mřížek Tlačítek (Grids of Buttons):
Možnost vytvářet, pojmenovávat a mazat mřížky.
Pro každou mřížku definovat, zda bude obsahovat vstupy (Sources) nebo výstupy (Destinations).
Možnost přidávat tlačítka do mřížky.
Pro každé tlačítko:
Přiřadit existující nakonfigurovaný vstup/výstup (podle typu mřížky).
Možnost nastavit pořadí tlačítek v mřížce.
4.1.5. Ukládání Konfigurace:
Veškerá uživatelská konfigurace (IP/Port, kategorie, I/O mapování, mřížky) musí být persistentní. Pro tuto verzi postačí uložení do Local Storage prohlížeče.
4.2. Ovládací Rozhraní (Control Interface)

4.2.1. Rozložení:
Hlavní obrazovka rozdělena na dvě hlavní části vedle sebe:
Levá část: Zobrazuje vybranou mřížku tlačítek se vstupy (Sources).
Pravá část: Zobrazuje vybranou mřížku tlačítek s výstupy (Destinations).
Možnost vybrat, která nakonfigurovaná mřížka se má v levé a pravé části zobrazit.
Jasně viditelné tlačítko "TAKE".
4.2.2. Zobrazení Tlačítek:
Tlačítka v mřížkách zobrazují uživatelsky přátelské názvy přiřazených vstupů/výstupů.
Vizuální indikace stavu (vybraná destinace, vybraný vstup).
4.2.3. Proces Přepnutí (Routing Workflow):
Kliknutí na tlačítko destinace (pravá mřížka) -> zvýraznění.
Kliknutí na tlačítko vstupu (levá mřížka) -> zvýraznění.
Aktivace tlačítka "TAKE".
Kliknutí na "TAKE".
Odeslání příkazu .S do routeru pro video úroveň.
4.2.4. Zobrazení Aktuálního Stavu:
Aplikace musí v reálném čase (na základě .U zpráv) zobrazovat aktuálně připojený vstup pro každou destinaci zobrazenou v mřížce (např. malým písmem pod názvem destinace zobrazit přátelský název připojeného vstupu).
4.3. Komunikace s Routerem

4.3.1. Spojení:
Navázat a udržovat TCP/IP spojení s jedním nakonfigurovaným routerem.
Zpracovat stavy připojení.
Automatický pokus o znovupřipojení.
4.3.2. Odesílání Příkazů:
Po stisknutí "TAKE" sestavit a odeslat příkaz .SV{dest},{srce}(cr) [zdroj: 110, 112].
V: Pevně daná video úroveň.
{dest}: Fyzické číslo vybrané destinace.
{srce}: Fyzické číslo vybraného vstupu.
(cr): Znak Carriage Return (0x0D).
Všechny příkazy velkými písmeny [zdroj: 103].
4.3.3. Příjem a Zpracování Odpovědí:
Naslouchat a parsovat odpovědi routeru: .U, .A, .E, .P [zdroj: 243-248].
Aktualizovat UI a logovací okno na základě přijatých zpráv.
Zpracovat i neočekávané .U zprávy (real-time aktualizace) [zdroj: 263, 267].
Zpracovat neúplné zprávy.
4.4. Logovací Okno / Status Panel

Musí být přítomna sekce (např. panel ve spodní části obrazovky nebo samostatná záložka/okno), která zobrazuje historii událostí.
Logovat minimálně:
Stav připojení (Pokus o připojení na {IP}:{PORT}, Připojeno, Odpojeno, Chyba připojení: {detail chyby}).
Odeslané příkazy (např. SEND: .SV{dest},{srce}).
Přijaté odpovědi (např. RECV: .UV{dest},{srce}, RECV: .A, RECV: .E, RECV: .P).
Interní chyby aplikace (pokud relevantní).
Logy by měly mít časové razítko.
Možnost vymazat logy.
5. Nefunkční Požadavky

Responzivita: Plně responzivní design (desktop, tablet).
Výkon: Rychlá odezva UI, neblokující komunikace.
Spolehlivost: Stabilní připojení, korektní zpracování odpovědí a chyb, robustní znovupřipojení.
Použitelnost: Intuitivní a konfigurovatelné rozhraní.
Udržovatelnost: Čistý, strukturovaný a komentovaný kód.
6. UI/UX Design (Návrh)

Čistý, přehledný a moderní design.
Doporučení: Použít utility-first CSS framework jako Tailwind CSS pro snadnější dosažení responzivity a konzistentního vzhledu.
Jasné vizuální oddělení konfigurace a ovládání.
Vizuální zpětná vazba pro akce a stavy.
Konfigurovatelnost vzhledu (pokud možno) - např. velikost tlačítek, barevné schéma.
Responzivní layout.
7. Technické Detaily

Protokol: Evertz Quartz RCP-T01-1v6 (ASCII přes TCP).
Komunikace: TCP/IP Sockets.
Frontend Stack: HTML5, CSS3 (Tailwind CSS), JavaScript (framework volitelný).
Backend Stack: Node.js (s net modulem) nebo Python (s socket modulem) + WebSocket server pro komunikaci s frontendem.
Persistence: Local Storage.
const { app, BrowserWindow } = require('electron');
const path = require('path');
let mainWindow;

// Set the app's base path for use throughout the application
global.appRoot = path.resolve(__dirname);

// Import the server directly instead of spawning a process
function startServer() {
  try {
    // This will run the server code in the Electron process
    require('./server.js');
    console.log('Server started within Electron process');
  } catch (error) {
    console.error('Failed to start server:', error);
  }
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true
    },
    title: 'Router Control'
  });

  // Allow time for the server to start
  setTimeout(() => {
    mainWindow.loadURL('http://localhost:3000');
    
    // Uncomment for debugging
    // mainWindow.webContents.openDevTools();
  }, 1000);

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

app.on('ready', () => {
  startServer();
  createWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});
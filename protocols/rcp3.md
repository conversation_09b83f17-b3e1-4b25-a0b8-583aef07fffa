
RCP-3 Control Protocol

Overview
The RCP-3 Control Protocol is a packet based protocol used for sending commands to and receiving status from Routing Switcher System Controller.
The protocol can be used to send takes, set attributes, control locks etc., as well as monitor status, errors and alarms.
These operations enable you to create a well-featured custom control interface to the system controller. To create a custom control interface you need to create a client application on the External Control Device (ECD). This external device may be a standard PC, workstation, or purpose built embedded controller.


RCP-3 and the System Controller
The SC-3 and SC-4 system controllers are sophisticated controllers providing backward compatibility to previous generations of control panel and routing switchers. The degree of flexibility in mapping the older Partyline panels and AVS2 series routing switchers adds a degree of complexity to the control of the routing system that the controller is set up to address. To simplify the implementation of a remote control application using the RCP-3 protocol, the SC-3/SC-4 configuration should be accomplished using the U-NET mapping tables.
The UTAH-200 controller also uses the RCP-3 protocol with minor differences, which are specified in this document where necessary. Otherwise, the usage of the term SC-3 controller is synonymous with the terms SC-4 controller and UTAH-200 Controller.


How the RCP-3 Control Protocol works
Each command and status message has a similar packet format, which consists of a header followed by the required data. The header contains information to identify the protocol object being accessed, the command or status type and the length and checksum of the appended data. The data portion contains information based on the type of command or status.

Once communication has been established to the controller, it is simple to format and send the commands required by your custom control interface. Switch commands transmitted by the ECD, are received by the System Controller, that will then issue a corresponding command to the Router Matrix. A successful TAKE will be confirmed by a status message transmitted from the System Controller to the ECD. If no status is returned then the operation was unsuccessful and generally an error is returned.
See Section 3.5 for a list of errors.

The System Controller may also generate unsolicited status at any time due to operations on panels connected to the controller or by commands received by the controller through serial and network connections. This unsolicited status feature can be turned on and off by using the Verbosity Command. Refer to section 3.2.2 for details.
Network Connections
Network connections for the RCP-3 Control Protocol utilize the Standard 10Mbit Ethernet TCP/IP protocol via sockets. The SC-4 can support 100Mbit Ethernet. To send commands and receive status over a network connection you must open a client socket with the IP address of the System Controller using Port 5001 for the SC-3/SC-4 and Port 5002 for the UTAH-200. The ECD and the system controller must be on the same sub-net (or a gateway must be used). When using socket communications with the UTAH-200 controller, you must precede each command packet with the four-byte pattern as shown below in section 3.0.4 Serial Connections. The SC-3 and SC-4 do not use this synchronization pattern for network connections.

Note:
If you have a redundant system, each controller board must use different IP Addresses (except for the SC-4 which stores the IP address with the chassis). However, only the Master Board is connected to the network. The computer and the controller must be connected through a hub or concentrator. Connection directly between the computer and controller can also be made by means of a special “null” cable.

Once the socket has been opened, simply filling a buffer with the packet information and writing it directly to the output stream of the socket, can send commands. See 3.0.5: Packet Format for more information. Status can be received by reading from the input stream of the socket.

Serial Connections
To make a serial connection from your computer to an SC-3 or UTAH-200 controller, you must connect a standard RS-232 or RS-422 cable from a serial port on your computer to a controller serial port. The SC-4 does not support RCP-3 serial protocol at this time.
NOTE:
The controller serial port should be first be configured by using the SC-3 diagnostic port (port 0). The UTAH-200 serial ports can be configured by using the front panel or the diagnostic serial port on the front of the controller board inside the chassis.
The serial port on the computer should then be set to 38.4 kBaud, no parity, 8 data bits and 1 stop bit.
Once the serial port has been opened, commands can be sent by filling a buffer with the packet information (see Packet Format below) and writing them directly to the port, preceded by a four-byte pattern. This four-byte pattern must precede each command packet and is show below in hexadecimal format for the SC-3 controller.

Open serial connections should be closed once necessary changes have been made.
Too many open serial connections will eventually limit or shut down access to the SC-3 Controller.
The SC-3 Controller uses the following pattern:
(Pre-pended to all serial port commands - opens the serial connection)



To close the serial connection:



The UTAH-200 Controller uses the following pattern:
(Pre-pended to all serial port commands - opens the serial connection)



To close the serial connection:



Status can be received by reading from the serial port. Again, the same four-byte prefix signals the beginning of each packet.

Packet Format
A complete command or status packet includes:
A fixed-length header.
A block of data. (The size and format of which depends on the command or status type)
Table representations of packet format in this document consist of a matrix of boxes where each box represents a byte of data. Bytes are meant to be sent out in reading order, left to right beginning with the upper left byte. Entries labeled padding are present in the packet and their value should be set to zero when sending commands and ignored when receiving status. The data block should not exceed 256 bytes otherwise the command should be divided into multiple commands.

Packet Header Format
The headers for commands and status are identical. These headers are six bytes in length and are formatted as follows:



The Interface Value identifies the protocol object this packet is to be sent to or has come from.
The Interface Values for each different protocol object are found in Section 3.1.1.

The Command / Status Type value identifies the command being sent or the status being received. The values for each command and status type are also found in Section 3.1.1.

The Data Checksum is a simple byte sum of the data that follows the header. The SC-3 uses this checksum to ensure the integrity of each command packet. Your custom control interface may also use this checksum to verify each status packet.

The Data Length is simply the number of bytes of data that follow the header.


Packet Data Format
Each header is followed by specific data depending on the interface and the command or status type of the packet.
These formats are found in Section 3.2.

Error Packet Formats
Error status may be generated unsolicited at any time by the SC-3. Error status packets have the same packet format as commands and status.
These formats are listed in Section 3.2.

Commands and Status
This section lists the command and status packet data formats for the RCP-3 Control Protocol. Status that is not documented here should be ignored. The tables below consist of a matrix of boxes where each box represents a byte of data. Bytes are meant to be sent out in reading order, left to right beginning with the upper left byte. Entries labeled padding are present in the packet but their value is irrelevant.

Command and Status Formats
Unless otherwise stated the value for the interface in the packet header is 12 hexadecimal.
The Command and Status Type Values for the header are listed in hexadecimal format in the headings below.
Source and Destinations are represented individually in binary arithmetic as 16 bit values. Levels are bit mapped so that each level is represented by an individual bit. Bit 0 represents level 1, bit 1 represents level 2 etc. The protocol supports a maximum of 32 levels.

Command Messages
This section provides details of the individual commands and associated response from the system controller.

Ping Command
This command is useful for verifying the connection between the ECD and the System Controller and may be used with both serial and Ethernet connections.



To send the command, set interface in the packet header to 02 for a serial connection or 03 for a socket connection. The command type for either serial or socket connections are FE.
There is NO DATA associated with the ping command.
If the system controller is connected, a ping status will be generated with the status type in the packet header set to FD. There is one byte of data whose value is 07. The status indicates simply that the connection between the ECD and the System Controller is working.

Verbosity Command
In order for the SC-3 to send unsolicited status on a particular interface, a verbosity message must be sent. The SC-4 does not require this message.



To send this command, set the interface in the packet header to 04.

The verbosity level should be set to 02 in order to receive status. A verbosity level of 00
will turn off unsolicited status messages.

Verbosity Command and Status Data Format



Take Command
The Take Command allows a TAKE to be made on multiple levels of a router system. The status packet returned by the controller serves as an acknowledgement that the command was received.


Take Command and Status Data Format.




Attribute Command
The Attribute Command allows attributes to be set for a destination of a Utah-300 router on multiple levels. The status packet returned by the controller serves as an acknowledgement that the command was received.



Attribute Command Message Format.


Valid attribute values are listed below in hexadecimal format:

Attribute Codes




Monitor Matrix Take Command
The monitor TAKE command allows selections to be made to the Utah-300 Monitor Matrix on multiple levels. The controller responds with a status packet confirming a successful TAKE.



Monitor TAKE Command Message Format


Disconnect Command
The Disconnect Command disconnects a destination on multiple levels. The controller responds with a status packet that serves as an acknowledgement that the command was received correctly. This disconnect command will switch a predefined source to the specified destination. This feature works with the SC-3 and SC-4 controllers only. See section 2.1.14 Physical Disconnect Command for further disconnect information.




Disconnect Command Message Format




Salvo Command
This command sends a Salvo. The controller responds with a status packet that serves as an acknowledgement that the controller received the command correctly. The salvo command is not yet supported on the SC-4 controller.




Salvo Command Message Format


Status Command
The status command requests information on the status of the router size. There is NO DATA associated with the command. The controller returns the status packet format shown below.



Status Command Message Format




Get Matrix Command
The Get Matrix Command allows you to request the status of the router matrix to see what sources are connected to each destination. The command and status packet formats differ. In the command the start dest value tells what destination you want information on and num dests tells how many subsequent consecutive destinations you want information on.



Get Matrix Command Message Format



The controller responds with status that may be in multiple packets. Each status packet has the same format as the command but is followed by matrix data. The matrix data consists of two bytes of source information for each of the 32 levels and is repeated for each destination beginning with start dest and ending when num dests has been reached.
Get Matrix Response Message Format




Get Matrix Attributes Command
The Get Matrix Attributes Command allows you to request the current attributes of the Router matrix for each destination. The command and response packet formats differ.



Status can be returned in multiple packets. Each response packet identifies a block of destinations and is followed by the associated matrix attribute data. The matrix data consists of one byte of attribute information for each of the 32 levels and is repeated for each destination beginning with start dest and ending when num dests has been reached.

Get Matrix Attributes Response Data Format


Get Monitor Matrix Command
The Get Monitor Matrix Command allows you to request the status of the router monitor matrix to see what destinations are connected for each level. There is NO DATA associated with the command.




Get Monitor Matrix Response Data Format




Set Lock Command
The Set Lock command allows a U-Net destination to be protected from subsequent TAKEs in a specified manner. This operation is identical to the 0B/1B lock operation of CSP and other Partyline panels. In the command the lock type value specifies the 0B/1B lock type for each level. A bit set is a 1B lock. A bit cleared is a 0B lock. The level contains a bit set for each level that is to be locked. The panel value tells which U-Net node number to use as the panel that set the lock. The system will behave just as if a lock were set at the panel specified. There is NO DATA associated with the status.  It is simply an acknowledgement that the command was received correctly.




Set Lock Command Data Format


Get Lock Command
The Get Lock Command allows you to query the controller for all lock information. You can find out which destinations are protected by which panels. There is NO DATA associated with the command. Status can be returned in multiple packets.



The lock data for each destination consists of a lock type, level and two bytes of panel information for each of the 32 levels. This data is repeated for each destination beginning with start dest and ending when num dests has been reached. Each byte of panel information simply tells what panel made the lock for that respective level.
Note that the panel information is only important for 0B locks.

Get Lock Response Data Format


Clear Lock Command
U-NET panels (such as the SCP series panels) have the ability to lock any specific output. The Clear Lock Command allows locks on a destination to be cleared. In the command, the lock type is not used and should be set to zero. The level contains a bit set for each level that is to be cleared. The panel value tells which U-Net node number to use as the panel clearing the lock. The system will behave just as if a lock were cleared at the panel specified. There is NO DATA associated with the status.  It is simply an acknowledgement that the command was received correctly.




Clear Lock Command Data Format




Physical Disconnect Command
The Physical Disconnect Command disconnects a destination on multiple levels. The controller responds with a status packet that serves as an acknowledgement that the command was received correctly. The disconnect command will physically disconnect the specified destination in the router hardware.
This feature works with the UTAH-200 controller only.
See section 2.1.5 Disconnect Command for further disconnect information.




Disconnect Command Message Format




Command Code Summary
This table lists the Command Codes used in the RCP-3 Protocol. The first column indicates the status code in hexadecimal notation.
The second column indicates the direction of the message is from the External Control Device (ECD) to the System Controller (SC).
The third column indicates the direction of communications is from the SC to the ECD.

Table 7: RCP-3 COMMAND CODE SUMMARY


Extended Command Messages
This section provides details of the individual extended commands for the system controller. These commands are not officially part of the published RCP-3 protocol. They are included here for informational purposes. The syntax and semantics of these commands may change in the future. The user would normally use the RMS program or other software directly from the company to perform these functions.

Create AVS Command
This command is used with the SC-3 only. It enables the AVS router subsystem and sets the operating parameters. It may be necessary to send the Destroy AVS command prior to creating a new AVS subsystem with different parameters.



To send this command, set the interface in the packet header to hex 0E. A sync source of 00 corresponds to 525 sync (NTSC), 01 is for 625 sync (PAL).

Create AVS Command and Status Message Format



Get AVS Command
This command is used with the SC-3 only. It enables the user to request the status of the AVS subsystem and operating parameters.



To send this command, set the interface in the packet header to hex 0E. A sync source of 00 corresponds to 525 sync (NTSC), 01 is for 625 sync (PAL).

Get AVS Response Message Format



Destroy AVS Command
This command is used with the SC-3 only. It disables the AVS router subsystem. To send this command, set the interface in the packet header to hex 0E.



Create UTAH Command
This command is used with the SC-3 only. It enables the UTAH router subsystem and sets the operating parameters. It may be necessary to send the Destroy UTAH command prior to creating a new UTAH subsystem with different parameters.





To send this command, set the interface in the packet header to hex 11. The sync mode can be one of three options: 01 for 525 (NTSC) only, 02 for 625 (PAL) only, or 00 for DUAL sync.

Create UTAH Command and Status Message Format




Get UTAH Command
This command is used with the SC-3 or SC-4. It enables the user to request the status of the UTAH subsystem and operating parameters.



To send this command, set the interface in the packet header to hex 11. The sync mode can be one of three options: 01 for 525 (NTSC) only, 02 for 625 (PAL) only, or 00 for DUAL sync.

Get UTAH Response Message Format



Destroy UTAH Command
This command is used with the SC-3 only. It disables the UTAH router subsystem. To send this command, set the interface in the packet header to hex 11.



Create SCP Command
This command is used with the SC-3 only. It enables the SCP panel subsystem and sets the operating parameters. It may be necessary to send the Destroy SCP command prior to creating a new SCP panel subsystem with different parameters.



To send this command, set the interface in the packet header to hex 12.

Create SCP Command and Status Message Format



Get SCP Command
This command is used with the SC-3 or SC-4. It enables the user to request the status of the SCP panel subsystem and operating parameters.



To send this command, set the interface in the packet header to hex 12.

Get UTAH Response Message Format




Destroy SCP Command
This command is used with the SC-3 only. It disables the SCP panel subsystem. To send this command, set the interface in the packet header to hex 12.



Mapping Command
The mapping command allows configuration of the logical router system. Levels can be rearranged and different router types supported. A single router chassis can also be mapped into multiple logical levels.

This command is used with either the SC-3 or SC-4. The SC-3 will not function without initializing the mapping table using this command.  Often this is done at the factory before the system is delivered to the end user. The SC-4 has a simple default mapping that will normally be sufficient for most cases but can be overridden using this command.



Each entry in the mapping table defines a logical level. Start entry and num entry values tell which entries are to be updated by the command. The remaining values shown below define the logical level for an entry and are repeated as specified by num entries.

In general, the entire table should be updated with one series of commands, starting with a first entry of zero. Source and destination values start at zero but levels begin at one. The level type is 1 for AVS routers and 2 for the UTAH series of routers. The SC-4 does not support AVS routers at this time.

Mapping Command and Status Data Format.



Get Mapping Command
The get mapping command requests the current mapping configuration.



See section 3.3.10 Mapping Command for more information about this data format.

Get Mapping Status Data Format.


SCP Panel Command Messages
This section provides details of the individual SCP panel programming commands. These commands are not officially part of the published RCP-3 protocol. They are included here for informational purposes. The syntax and semantics of these commands may change in the future. The user would normally use the RMS program or other software directly from the company to perform these functions.

The SCP programming commands are different from other RCP-3 commands because the system controller simply acts as a gateway to the U-Net panel network. Because of this, panel commands need two headers, one for the controller and one for the panel. The header for the controller has the same format as the headers for other RCP-3 commands and is the same for all SCP panel commands. This header should precede all panel command messages and is documented in the following table.



The data checksum is calculated as documented in section 3.0.6 and is calculated on the panel header and panel data.

The data length should include the size of the panel header and the panel data.

The panel header should be sent immediately after the controller header. The panel header is different in format from the controller header. It is twelve bytes in length and is formatted as follows:



The node ID identifies the panel to be programmed on the U-Net network.

The command type value identifies the command being sent. The command type values for each SCP panel command are different and are documented in the following subsections.

The send interface should be set to 02 for serial connections and 03 for socket Ethernet connections.

The dest interface should always be set to 12 hex.

The data length is the size of the header (12) plus the size of the data that follows the command.

The data for each command will immediately follow the panel header. Each command has a different data format. These formats are documented in the following subsections.

After a series of panel programming commands are sent the end reprogramming session command must be sent to get the panel out of reprogramming mode. This command is documented in section 3.4.15.
Set Panel ID Command

An arbitrary panel ID string can be stored on each SCP panel for identification purposes. This ID does not affect panel operation.




Get Panel ID Command

The panel ID string can be requested.



Set Source Group Name Command

The source group names can be set and associated with keypad buttons on certain panels such as the SCP-XY/16, SCP-2 and SCP-SX/MX models.



Set Source Group Name Data Format.





Start entry identifies the beginning entry of the command. Num entries tells how many entries are in the command. Each entry contains the information for one device group.
The name, key and num group entries values are repeated as many times as specified in
num entries.

The key values range from 00 to 13 hex and correspond to the different keypad keys. The num group entries value tells how many source name entries are in the group. Source name entries are programmed with the set source name command.


Get Source Group Name Command

The source group names can requested from certain panels such as the SCP-XY/16, SCP- 2 and SCP-SX/MX models.




Get Source Group Name Response Data Format.





Start entry identifies the beginning entry of the command. Num entries tells how many entries are in the command. Each entry contains the information for one device group.
The name, key and num group entries values are repeated as many times as specified in
num entries.
The key values range from 00 to 13 hex and correspond to the different keypad keys. The num group entries value tells how many source name entries are in the group. Source name entries are programmed with the set source name command.


Clear Source Group Name Command

This command clears the source group entries of a panel. This is useful to prepare the panel to receive new entries.



Set Destination Group Name Command

The destination group names can be set and associated with keypad buttons on certain panels such as the SCP-XY/16, SCP-2 and SCP-SX/MX models.



Set Destination Group Name Data Format.





Start entry identifies the beginning entry of the command. Num entries tells how many entries are in the command. Each entry contains the information for one device group.
The name, key and num group entries values are repeated as many times as specified in
num entries.

The key values range from 00 to 13 hex and correspond to the different keypad keys. The num group entries value tells how many destination name entries are in the group. Destination name entries are programmed with the set destination name command.


Get Destination Group Name Command
The destination group names can requested from certain panels such as the SCP-XY/16, SCP-2 and SCP-SX/MX models.



Get Destination Group Name Response Data Format.





Start entry identifies the beginning entry of the command. Num entries tells how many entries are in the command. Each entry contains the information for one device group.
The name, key and num group entries values are repeated as many times as specified in
num entries.

The key values range from 00 to 13 hex and correspond to the different keypad keys. The num group entries value tells how many destination name entries are in the group. Destination name entries are programmed with the set destination name command.


Clear Destination Group Name Command

This command clears the destination group entries of a panel. This is useful to prepare the panel to receive new entries.




Set Source Name Command

The source names can be set with this command. Some panels have direct source buttons such as the SCP-XY/16, SCP-2 and SCP-32 models.  These can also be assigned with this command.



Set Source Name Data Format.





Start entry identifies the beginning entry of the command. Num entries tells how many entries are in the command. Each entry contains the information for one source device. The name, extension, key and numeric values are repeated as many times as specified in num entries. Num entries should not exceed 5 for this command type, so multiple commands are generally required to specify the whole source table.

The source name can be up to eight characters long but will typically have five characters corresponding to the group name at the beginning and then three characters for the extension number. The extension value is simply a numeric representation of the ASCII extension in the source name. The key values range from 1A to 39 hex and correspond to the different direct source keys. However, some panels do not have this many keys dedicated to direct source operation. Sources without a direct source assignment should set this value to a –1 (FFFF hex). The numeric value for each of 16 possible levels correspond to the input on the router level. Unused levels should be set to
–1 (FFFF hex).


Get Source Name Command

The source names can be retrieved with this command.



Get Source Name Response Data Format.





Start entry identifies the beginning entry of the command. Num entries tells how many entries are in the command. Each entry contains the information for one source device. The name, extension, key and numeric values are repeated as many times as specified in num entries. Num entries should not exceed 5 for this command type, so multiple commands are generally required to specify the whole source table.

The source name can be up to eight characters long but will typically have five characters corresponding to the group name at the beginning and then three characters for the extension number. The extension value is simply a numeric representation of the ASCII extension in the source name. The key values range from 1A to 39 hex and correspond to the different direct source keys. However, some panels do not have this many keys dedicated to direct source operation. Sources without a direct source assignment should set this value to a –1 (FFFF hex). The numeric value for each of 16 possible levels correspond to the input on the router level. Unused levels should be set to
–1 (FFFF hex).


Clear Source Name Command

This command clears the source entries of a panel. This is useful to prepare the panel to receive new entries.



Set Destination Name Command

The destination names can be set with this command. Some panels have direct destination buttons such as the SCP-XY/16, SCP-2 and SCP-32 models. These can also be assigned with this command.



Set Destination Name Data Format.





Start entry identifies the beginning entry of the command. Num entries tells how many entries are in the command. Each entry contains the information for one source device. The dest name, extension, key and numeric values are repeated as many times as specified in num entries. Num entries should not exceed 5 for this command type, so multiple commands are generally required to specify the whole source table.

The dest name can be up to eight characters long but will typically have five characters corresponding to the group name at the beginning and then three characters for the
extension number. The extension value is simply a numeric representation of the ASCII extension in the source name. The key values range from 16 to 19 hex and correspond to the different direct destination keys. However, some panels do not have this many keys dedicated to direct destination operation. Destinations without a direct destiantion assignment should set this value to a –1 (FFFF hex). The numeric value for each of 16 possible levels correspond to the output on the router level. Unused levels should be set to –1 (FFFF hex).


Get Destination Name Command

The destination names can be retrieved with this command.



Get Destination Name Response Data Format.





Start entry identifies the beginning entry of the command. Num entries tells how many entries are in the command. Each entry contains the information for one destination device. The dest name, extension, key and numeric values are repeated as many times as specified in num entries. Num entries should not exceed 5 for this command type, so multiple commands are generally required to specify the whole source table.

The dest name can be up to eight characters long but will typically have five characters corresponding to the group name at the beginning and then three characters for the extension number. The extension value is simply a numeric representation of the ASCII extension in the destination name. The key values range from 16 to 19 hex and correspond to the different direct destination keys. However, some panels do not have this many keys dedicated to direct destination operation. Destinations without a direct destination assignment should set this value to a –1 (FFFF hex). The numeric value for each of 16 possible levels correspond to the output on the router level. Unused levels should be set to –1 (FFFF hex).


Clear Destination Name Command

This command clears the destination entries of a panel. This is useful to prepare the panel to receive new entries.



End Reprogramming Session Command

This command terminates the programming process and takes the panel out of reprogramming mode.



Status Messages
This section details status messages from the controller. The controller sends these messages automatically, without instigation by the ECD.

System Take Status
The System Take Status is a special status generated by the controller in response to any system wide TAKE inclusive of TAKEs made by control panels or other control devices.



System Take Status Data Format




System Attribute Status
The System Attribute Status is a special status generated by the controller any time that ATTRIBUTES are changed on any part of the matrix configured in the System Controller, inclusive of changes made by control panels or other control devices.




System Attribute Status Data Format




System Monitor Matrix Take Status
The System Monitor Take Status is a special status generated by the controller any time Monitor Matrix TAKE’s are made on any matrix configured in the System controller.



System Monitor Take Status Data Format


System Lock Status
The System Lock Status is a special status generated any time lock changes are made that correspond to destinations on any matrix configured in the System Controller. A Status Type of 0 means this is a Lock Set Status. Any other value indicates a Lock Clear Status. A Lock Type value of 0 means this is a 1B Lock Status.  Any other value indicates a 0B Lock Status.



System Lock Status Data Format




Status Message Code Summary
This table lists the Status Message Codes used in the RCP-3 Protocol. The first column indicates the status code in hexadecimal notation.
The second column indicates the direction of the message is from the External Control Device (ECD) to the System Controller (SC).
The third column indicates the direction of communications is from the SC to the ECD.


RCP-3 STATUS CODE TABLE



Error Status Formats
Error Status may be received from the System controller at any time for a number of different reasons. Some indicate serious problems and some are simply informational. The packet format for error status is similar to that of other status packets. The Header Interface and Status Type values are listed under the headings below.
The data format for all Error Status Packets consists of eight bytes. The specific meaning of the data for each error status is undocumented and is intended to be used for engineering troubleshooting purposes only.


Panel Error Status
The Interface Value in the packet header for panel error status is 07 hexadecimal. The
Status Type for each error is listed below in hexadecimal format:





Error Status
The interface value in the packet header for U-Net error status is 0x0A hexadecimal. The status type for each error is listed below in hexadecimal format:


Redundancy Error Status
The Interface value in the packet header for redundancy error status is 0x0C
hexadecimal.
The Status Type for each error is listed below in hexadecimal format:





Watchdog Error Status
The interface value in the packet header for watchdog error status is 0D hexadecimal. The Status Type for each error is listed below in hexadecimal format:




General SC-3 Error Status
The interface value in the packet header for general SC-3 error status is 0D hexadecimal. The Status Type for each error is listed below in hexadecimal format:



APPENDIX

ASCII Code Table


Table A-1: ASCII Codes (Hexadecimal)











This page intentionally left blank
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Router Control</title>
    <style>
        :root {
            --monitor-button-size: 100px;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #a0a0a0; /* Darker grey */
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            background-color: #a0a0a0; /* Darker grey */
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); /* Subtle shadow for depth */
            width: 100%;
            max-width: none; /* Remove any max-width constraint */
        }

        /* Tab Navigation: 3 larger square buttons, centered at top middle */
        .tab-navigation {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            gap: 16px;
            width: 100%;
            position: sticky;
            top: 0;
            background-color: #a0a0a0;
            padding: 10px 0;
            z-index: 100;
        }
        .tab-button::before {
            display: none;
        }
        .tab-button::after {
            display: none !important;
        }
        .tab-button {
            width: 128px;
            height: 128px;
            min-width: 128px;
            min-height: 128px;
            max-width: 128px;
            max-height: 128px;
            font-size: 14px;
            border-radius: 16px;
            letter-spacing: 0.5px;
            white-space: normal;
            line-height: 1.1;
            padding: 6px 8px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .tab-button.active {
            background: #ffc107 !important;
            color: #222 !important;
            border: 2.5px solid silver !important; /* Silver outline for active tab */
            box-shadow: 0 0 5px silver !important; /* Silver glow effect */
            text-shadow: 0 0 1px silver, 0 0 2px silver; /* Silver outline for active tab text */
        }
        .tab-button:not(.active) {
            background: #e0e0e0;
            color: #000000; /* Changed from light gray to black */
            border: 2.5px solid #ffc107;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            background-color: #a0a0a0; /* Darker grey to match the rest of the UI */
            padding: 10px;
            border-radius: 5px;
            border: 2px solid #a0a0a0; /* Invisible outline */
        }
        .status-indicator {
            display: flex;
            align-items: center;
        }
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background-color: #5cb85c; }
        .status-disconnected { background-color: #d9534f; }
        .status-error { background-color: #ff8c00; /* Orange color for error state */ }
        .connect-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .connect-button {
            padding: 8px 15px;
            font-weight: bold;
        }
        .connect {
            background-color: #5cb85c;
            color: white;
        }
        .disconnect {
            background-color: #d9534f;
            color: white;
        }
        .control-panel {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .panel-section {
            border: 2px solid #ffc107; /* Yellow outline */
            border-radius: 5px;
            padding: 15px;
            background-color: #a0a0a0; /* Darker grey for all sections */
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        .router-section-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .router-section-container {
            border: 2px solid #ffc107;
            border-radius: 8px;
            background: #a0a0a0;
            padding: 15px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .routing-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .routing-column {
            flex: 1;
        }
        h2 {
            margin-top: 0;
        }
        .button-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
        }
        button {
            padding: 10px 15px;
            cursor: pointer;
            border-radius: 5px;
            border: 1px solid #ccc;
            background-color: #f8f8f8;
            font-size: 14px;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        button::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: 0.3s;
        }

        button:hover {
            background-color: #e8e8e8;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        button:hover::after {
            left: 100%;
        }

        button:active {
            transform: translateY(1px);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        button.input {
            background-color: #d4edda;
        }
        button.input.active {
            background-color: #5cb85c;
            color: white;
        }
        button.output {
            background-color: #d1ecf1;
        }
        button.output.active {
            background-color: #17a2b8;
            color: white;
        }
        button.take {
            background-color: #28a745; /* Changed from red to green */
            color: white;
            font-weight: bold;
        }
        button.take:hover {
            background-color: #218838; /* Darker green on hover */
        }
        button.take:disabled {
            background-color: #d4edda;
            cursor: not-allowed;
        }
        button.lock {
            background-color: #dc3545; /* Changed from yellow to red */
            color: white;
        }
        button.lock:hover {
            background-color: #c82333; /* Darker red on hover */
        }
        button.lock.active {
            background-color: #bd2130; /* Even darker red when active */
            color: white;
        }
        button.refresh, button.lock, button.take {
            height: 48px;
            min-width: 90px;
            font-size: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .log-panel {
            border: 2px solid #ffc107; /* Yellow outline */
            border-radius: 5px;
            padding: 10px;
            height: 150px;
            overflow-y: auto;
            background-color: #a0a0a0; /* Darker grey */
            margin-top: 20px;
        }
        .log-entry {
            margin-bottom: 5px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry.command { color: #007bff; }
        .log-entry.response { color: #28a745; }
        .log-entry.error { color: #dc3545; }
        select {
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ccc;
            font-size: 14px;
            width: 100%;
            max-width: 300px;
            margin-bottom: 15px;
        }
        select#destination-select,
        select#source-select,
        #wall-source-select {
            display: none !important;
        }
        .status-panel {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .lock-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #dc3545;
            margin-left: 10px;
        }
        .lock-indicator.hidden {
            display: none;
        }
        .flex-row {
            display: flex;
            align-items: center;
        }
        .category-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        .category-button {
            padding: 8px 12px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 5px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .category-button::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: 0.3s;
        }

        .category-button:hover {
            background-color: #e0e0e0;
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .category-button:hover::after {
            left: 100%;
        }

        .category-button.active {
            background-color: #ffc107;
            color: #333;
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
        }

        .status-window {
            display: flex;
            justify-content: space-between;
            background-color: #000000;
            color: #ffc107;
            padding: 12px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-family: monospace;
            font-size: 14px;
            border: 2px solid #333;
        }
        .status-window.locked {
            border: 2px solid #FF0000;
        }
        .status-label {
            font-weight: bold;
        }
        .square-button-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 12px;
            min-height: 210px; /* Height to accommodate 3 rows of buttons (60px + 8px gap) * 3 */
            margin-bottom: 15px;
            overflow-y: auto;
        }
        .square-button {
            width: 70px; /* Increased from 60px to accommodate 8 characters */
            height: 70px; /* Increased height to maintain square proportion */
            margin: 3px;
            border: 1px solid #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            text-align: center;
            cursor: pointer;
            border-radius: 5px;
            padding: 4px;
            word-break: break-word;
            background-color: #c0c0c0; /* Silver background */
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .square-button::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: 0.4s;
        }

        .square-button:hover {
            background-color: #b0b0b0;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .square-button:hover::after {
            left: 100%;
        }

        .square-button.selected {
            border: 2px solid #007bff;
            background-color: #e7f1ff;
        }
        .square-button.output {
            background-color: #d1ecf1;
        }
        .square-button.output.selected {
            background-color: #ffc107;
            color: #333;
        }
        .square-button.input {
            background-color: #d4edda;
        }
        .square-button.input.selected {
            background-color: #ffc107;
            color: #333;
        }
        .locked-text {
            color: #dc3545; /* Red text */
            font-weight: bold;
            margin-left: 5px;
            display: none; /* Hide by default */
        }

        /* Category Management Styles - Updated for side by side layout */
        .category-management {
            margin-top: 20px;
        }

        .category-form {
            background-color: #a0a0a0;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 2px solid #ffc107; /* Yellow outline */
        }

        .category-side-by-side {
            display: flex;
            gap: 20px;
            justify-content: space-between;
        }

        .category-column {
            display: flex;
            flex-direction: column;
            align-items: stretch;
            flex: 1;
        }

        .category-column > div:first-child {
            width: 100%;
        }

        .category-list {
            /* max-height: 400px; */
            /* overflow-y: auto; */
        }

        .category-item {
            background-color: #a0a0a0;
            border: 2px solid #ffc107; /* Yellow outline */
            border-radius: 5px;
            padding: 12px;
            margin-bottom: 10px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .category-item .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .category-item .category-name {
            font-weight: bold;
            font-size: 16px;
        }

        .category-item .category-controls {
            display: flex;
            gap: 5px;
        }

        .category-item .edit-btn,
        .category-item .delete-btn {
            padding: 4px 8px;
            font-size: 12px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
        }

        .category-item .edit-btn:hover {
            background-color: #007bff;
            color: white;
        }

        .category-item .delete-btn:hover {
            background-color: #dc3545;
            color: white;
        }

        .category-item .io-list {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .category-item .io-tag {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 3px 6px;
            font-size: 12px;
        }

        .ios-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            max-height: 200px;
            overflow-y: auto;
            padding: 10px;
            background-color: #a0a0a0;
            border: 2px solid #ffc107; /* Yellow outline */
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .ios-selector .io-item.selected {
            background-color: #28a745 !important;
            color: #fff !important;
            border: 2px solid #218838 !important;
        }

        .section-header.category-title {
            color: #c0c0c0 !important;
            background: transparent !important;
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            letter-spacing: 2px;
            text-transform: uppercase;
            justify-content: center;
            border: none !important;
            box-shadow: none;
            width: 100%;
            text-shadow: 0 0 2px #000, 0 0 1px #000, 1px 1px 2px #000, -1px -1px 2px #000;
        }

        /* Monitors Tab Styles */
        .monitors-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .monitors-toolbar {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background-color: #a0a0a0; /* Darker grey to match the rest of the UI */
            border-radius: 5px;
            margin-bottom: 10px;
            border: 2px solid #a0a0a0 !important; /* Grey outline */
        }

        .size-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
            flex-grow: 1;
        }

        .size-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .size-control span {
            min-width: 50px;
            text-align: center;
        }

        .size-control input[type="range"] {
            width: 150px;
        }

        .monitor-board {
            min-height: 800px;
            width: 100%;
            background-color: #a0a0a0;
            border: 2px solid #ffc107; /* Yellow outline */
            border-radius: 5px;
            padding: 20px;
            position: relative;
        }

        .monitor-button {
            position: absolute;
            /* Remove width/height from CSS var since each button will have individual size */
            background-color: #c0c0c0; /* Silver background */
            border: 2px solid #ffc107;
            border-radius: 5px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: move;
            user-select: none;
            padding: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: width 0.3s, height 0.3s;
        }

        /* Monitor button controls */
        .monitor-button-controls {
            position: absolute;
            top: 5px;
            right: 5px;
            display: none;
            gap: 5px;
        }

        .monitor-button:hover .monitor-button-controls,
        .monitor-button.selected .monitor-button-controls {
            display: flex;
        }

        .monitor-button-rename,
        .monitor-button-remove,
        .monitor-button-copy,
        .monitor-button-paste {
            width: 20px;
            height: 20px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .monitor-button-rename:hover {
            background-color: #f8f8f8;
        }

        .monitor-button-remove:hover {
            background-color: #ff6b6b;
            color: white;
        }

        .monitor-button-copy:hover {
            background-color: #4caf50;
            color: white;
        }

        .monitor-button-paste:hover {
            background-color: #2196f3;
            color: white;
        }

        .monitor-button-label {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
        }

        .monitor-button-assignment {
            font-size: 12px;
            text-align: center;
            color: #333;
            background-color: #f0f0f0;
            padding: 2px 5px;
            border-radius: 3px;
            width: 90%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .monitor-button.destination-assigned {
            background-color: #d1ecf1;
            border-color: #17a2b8;
        }

        .monitor-button.source-assigned {
            background-color: #d4edda;
            border-color: #28a745;
        }

        .monitor-button.selected {
            border-color: #ffc107 !important;
            box-shadow: 0 0 8px rgba(255, 193, 7, 0.5);
        }

        /* Selected monitor button styling */
        .monitor-button.selected {
            border: 2px solid #28a745;
            box-shadow: 0 0 8px rgba(40, 167, 69, 0.5);
        }

        /* Assignment mode styling */
        .monitor-board.assignment-mode {
            background-color: rgba(0, 123, 255, 0.05);
        }

        /* Toast notification styles */
        .toast-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        .toast {
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            color: white;
            opacity: 0;
            transition: opacity 0.3s;
            max-width: 300px;
        }

        .toast.show {
            opacity: 1;
        }

        .toast.success {
            background-color: #28a745;
        }

        .toast.error {
            background-color: #dc3545;
        }

        .toast.info {
            background-color: #17a2b8;
        }

        .form-row {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .form-row label {
            min-width: 180px;
            margin-right: 10px;
        }

        .form-row input[type="text"],
        .form-row input[type="number"],
        .form-row select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ccc;
            flex: 1;
            max-width: 300px;
        }

        .protocol-info {
            margin: 15px 0;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .protocol-info h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #333;
        }

        .protocol-commands {
            margin-top: 10px;
        }

        .command-item {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .command-name {
            font-weight: bold;
            min-width: 100px;
            margin-right: 10px;
        }

        .command-item code {
            background-color: #e0e0e0;
            padding: 3px 6px;
            border-radius: 3px;
            font-family: monospace;
        }

        .action-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        .action-button:hover {
            background-color: #0056b3;
        }

        #copy-button-btn, #paste-button-btn {
            background-color: #6c757d;
        }

        #copy-button-btn:hover, #paste-button-btn:hover {
            background-color: #545b62;
        }

        .section-header {
            color: #c0c0c0 !important;
            background: transparent !important;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            letter-spacing: 2px;
            text-transform: uppercase;
            justify-content: center;
            border: none !important;
            box-shadow: none;
            width: 100%;
            text-shadow: 0 0 2px #000, 0 0 1px #000, 1px 1px 2px #000, -1px -1px 2px #000;
        }

        /* Monitor Wall Sources Title Customization */
        #monitor-wall .routing-column .section-header {
            color: #c0c0c0 !important;
            background: transparent !important;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            letter-spacing: 2px;
            text-transform: uppercase;
            justify-content: center;
            border: none !important;
            box-shadow: none;
            width: 100%;
            text-shadow: 0 0 2px #000, 0 0 1px #000, 1px 1px 2px #000;
        }
        #monitor-wall .routing-column .section-header .wall-take-btn {
            background-color: #28a745;
            color: white;
            font-weight: bold;
            padding: 10px 18px;
            border-radius: 5px;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.08);
            transition: background 0.2s;
            font-size: 16px;
            left: 10px;
            position: absolute;
        }
        #monitor-wall .routing-column .section-header .wall-take-btn:disabled {
            background-color: #d4edda;
            color: #888;
        }
        #monitor-wall .routing-column .section-header .wall-take-btn:hover:not(:disabled) {
            background-color: #218838;
        }

        /* Wall board resize handle */
        #wall-board-resize-handle {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 24px;
            height: 24px;
            background: rgba(0,0,0,0.15);
            border-radius: 0 0 5px 0;
            cursor: nwse-resize;
            z-index: 1000;
            display: block;
        }
        #wall-board-resize-handle:after {
            content: '';
            display: block;
            width: 16px;
            height: 16px;
            border-right: 3px solid #007bff;
            border-bottom: 3px solid #007bff;
            position: absolute;
            right: 4px;
            bottom: 4px;
            border-radius: 0 0 4px 0;
        }

        .side-by-side-panel {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .current-state-panel {
            flex: 1 1 0;
            max-width: 50%;
            min-width: 0;
        }
        .log-panel-section {
            flex: 1 1 0;
            max-width: 50%;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }
        .log-panel-section .log-panel {
            flex: 1 1 0;
            min-height: 150px;
            height: 100%;
        }
        @media (max-width: 900px) {
            .side-by-side-panel {
                flex-direction: column;
            }
            .current-state-panel, .log-panel-section {
                max-width: 100%;
            }
        }

        /* Monitor Wall Tab: Make all outlines (borders) except the layout canvas match the background grey */
        #monitor-wall .panel-section,
        #monitor-wall .section-header,
        #monitor-wall .category-buttons,
        #monitor-wall .routing-column,
        #monitor-wall .square-button-container,
        #monitor-wall .category-list,
        #monitor-wall .category-column,
        #monitor-wall .monitors-toolbar {
            border-color: #a0a0a0 !important;
        }
        #monitor-wall .monitor-board {
            border-color: #ffc107 !important; /* Keep yellow outline for layout canvas */
        }

        #monitor-wall .category-buttons#wall-source-categories {
            margin-top: 10px;
        }

        #monitor-wall .category-button.active {
            background-color: #ffc107;
            color: #333;
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
        }

        #wall-source-buttons .square-button.selected {
            background-color: #ffc107;
            color: #333;
        }

        .main-title, .router-section-title, .category-main-title, .category-form-title, .monitor-wall-title, .category-title-center, .log-title, .current-state-title, .section-header.category-title, .category-title-center, #monitor-wall .routing-column .section-header {
            color: #c0c0c0;
            background: transparent;
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            letter-spacing: 2px;
            margin-bottom: 20px;
            text-transform: uppercase;
            width: 100%;
            text-shadow: 0 0 2px #000, 0 0 1px #000, 1px 1px 2px #000, -1px -1px 2px #000;
        }

        /* Tab button specific styling */
        .tab-button {
            background: transparent;
            text-align: center;
            font-size: 16px; /* Smaller font size for tab buttons */
            font-weight: bold;
            letter-spacing: 1px; /* Reduced letter spacing */
            text-transform: uppercase;
            width: 100%;
        }

        /* Active tab gets silver text shadow, inactive tabs get black text with no shadow */
        .tab-button.active {
            text-shadow: 0 0 1px silver, 0 0 2px silver;
        }

        .tab-button:not(.active) {
            text-shadow: none;
        }

        /* Monitor Wall: created window, copy/paste, lock, etc. */
        #monitor-wall .action-button,
        #monitor-wall .action-button:active,
        #monitor-wall .action-button:focus {
            background-color: #ffc107 !important;
            color: #333 !important;
            border: 1px solid #ffc107 !important;
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.18);
        }
        #monitor-wall .action-button:hover {
            background-color: #ffdb5b !important;
            color: #222 !important;
        }
        /* Category: add category buttons yellow */
        #add-destination-category-btn,
        #add-source-category-btn {
            background-color: #ffc107 !important;
            color: #333 !important;
            border: 1px solid #ffc107 !important;
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.18);
        }
        #add-destination-category-btn:hover,
        #add-source-category-btn:hover {
            background-color: #ffdb5b !important;
            color: #222 !important;
        }

        /* Names Tab Styles */
        .names-edit-container {
            margin-bottom: 20px;
        }

        .names-edit-form {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: end;
        }

        .names-edit-form .form-row {
            margin-bottom: 0;
            min-width: 200px;
        }

        .names-lists-container {
            display: flex;
            gap: 20px;
            height: 600px;
        }

        .names-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: #a0a0a0;
            border: 2px solid #ffc107;
            border-radius: 5px;
            overflow: hidden;
        }

        .names-column-header {
            background-color: #909090;
            padding: 15px;
            border-bottom: 2px solid #ffc107;
        }

        .names-column-header h3 {
            margin: 0 0 10px 0;
            text-align: center;
        }

        .names-column-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
        }

        .names-search-input {
            flex: 1;
            padding: 6px 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }

        .names-count {
            font-weight: bold;
            color: #333;
            white-space: nowrap;
            background-color: #f0f0f0;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .names-list-container {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .names-list {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .names-item {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .names-item:hover {
            background-color: #e0e0e0;
        }

        .names-item.selected {
            background-color: #e7f1ff;
            border-color: #007bff;
        }

        .names-item-number {
            font-weight: bold;
            color: #007bff;
            font-size: 14px;
        }

        .names-item-name {
            margin-top: 5px;
            font-size: 13px;
        }

        .names-item-router-name {
            font-weight: bold;
            color: #333;
        }

        .names-item-other-names {
            color: #666;
            font-style: italic;
        }

        .names-item.empty {
            opacity: 0.6;
        }

        .names-item.empty .names-item-name {
            color: #999;
            font-style: italic;
        }

        /* Legacy table styles for backward compatibility */
        #names-table {
            width: 100%;
            border-collapse: collapse;
        }
        #names-table th, #names-table td {
            padding: 8px;
            text-align: left;
        }
        #names-table th {
            background-color: #a0a0a0;
            border-bottom: 2px solid #ffc107;
            position: sticky;
            top: 0;
        }
        #names-table tr {
            border-bottom: 1px solid #ddd;
        }
        #names-table tr:hover {
            background-color: #f5f5f5;
            cursor: pointer;
        }
        #names-table tr.selected {
            background-color: #e7f1ff;
        }
        .form-row {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .form-row label {
            width: 150px;
            margin-right: 10px;
        }
        .form-row input, .form-row select {
            flex: 1;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }

        /* Connection Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            padding: 25px 30px 20px;
            border-bottom: 1px solid #dee2e6;
            text-align: center;
        }

        .modal-header h2 {
            margin: 0;
            color: #495057;
            font-size: 24px;
            font-weight: bold;
        }

        .modal-body {
            padding: 25px 30px;
        }

        .connection-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #007bff;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-weight: bold;
            color: #495057;
            font-size: 14px;
        }

        .info-value {
            color: #007bff;
            font-weight: 600;
            font-size: 14px;
        }

        .modal-footer {
            padding: 20px 30px 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .modal-button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 160px;
        }

        .modal-button.connect-btn {
            background-color: #28a745;
            color: white;
        }

        .modal-button.connect-btn:hover {
            background-color: #218838;
            transform: translateY(-1px);
        }

        .modal-button.offline-btn {
            background-color: #6c757d;
            color: white;
        }

        .modal-button.offline-btn:hover {
            background-color: #5a6268;
            transform: translateY(-1px);
        }

        /* New Category List Styles */
        .category-list-new {
            padding: 0;
            max-height: 500px;
            overflow-y: auto;
        }

        .category-item {
            border-bottom: 1px solid #dee2e6;
            transition: all 0.2s ease;
        }

        .category-item:last-child {
            border-bottom: none;
        }

        .category-header {
            padding: 15px 20px;
            background: #f8f9fa;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .category-header:hover {
            background: #e9ecef;
        }

        .category-header.expanded {
            background: #e3f2fd;
            border-bottom: 1px solid #bbdefb;
        }

        .category-name {
            font-weight: bold;
            color: #495057;
            font-size: 14px;
        }

        .category-info {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 12px;
            color: #6c757d;
        }

        .category-count {
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }

        .category-actions {
            display: flex;
            gap: 5px;
        }

        .category-action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .category-action-btn.edit {
            background: #ffc107;
            color: #212529;
        }

        .category-action-btn.edit:hover {
            background: #e0a800;
        }

        .category-action-btn.delete {
            background: #dc3545;
            color: white;
        }

        .category-action-btn.delete:hover {
            background: #c82333;
        }

        .category-content {
            padding: 0;
            background: white;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .category-content.expanded {
            max-height: 300px;
            border-bottom: 1px solid #dee2e6;
        }

        .category-ios {
            padding: 15px 20px;
            background: #ffffff;
        }

        .ios-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 4px;
            max-height: 200px;
            overflow-y: auto;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }

        .io-number {
            padding: 4px 6px;
            background: #007bff;
            color: white;
            text-align: center;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }

        .expand-icon {
            transition: transform 0.2s ease;
            font-size: 12px;
            color: #6c757d;
        }

        .expand-icon.expanded {
            transform: rotate(90deg);
        }

        .special-category {
            background: #fff3cd !important;
            border-left: 4px solid #ffc107;
        }

        .special-category .category-name {
            color: #856404;
        }

        .special-category .category-name::after {
            content: " (Built-in)";
            font-size: 10px;
            font-weight: normal;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Tab Navigation - Moved to top and made square -->
        <div class="tab-navigation">
            <button class="tab-button" data-tab="setup">Setup</button>
            <button class="tab-button" data-tab="presets">Presets</button>
            <button class="tab-button active" data-tab="router-control">Router Control</button>
            <button class="tab-button" data-tab="names">Names</button>
            <button class="tab-button" data-tab="category-management">Category</button>
            <button class="tab-button" data-tab="monitor-wall">Monitor Wall</button>
        </div>

        <!-- Setup Tab -->
        <div id="setup" class="tab-content">
            <div class="panel-section">
                <h2 class="current-state-title">Setup</h2>
                <div class="control-panel">
                    <div class="router-section-row">
                        <div class="router-section-container">
                            <div class="router-section-title">Router Configuration</div>
                            <div class="form-row">
                                <label for="router-ip">Router IP Address:</label>
                                <input type="text" id="router-ip" placeholder="*************" value="*************">
                            </div>
                            <div class="form-row">
                                <label for="router-port">Router Port:</label>
                                <input type="number" id="router-port" placeholder="4000" value="4000">
                            </div>
                            <div class="form-row">
                                <label for="router-protocol">Router Protocol:</label>
                                <select id="router-protocol">
                                    <option value="quartz" selected>Quartz</option>
                                    <option value="rcp3">RCP3 (Utah)</option>
                                </select>
                            </div>
                            <div class="form-row">
                                <label for="router-inputs">Router Size - Inputs (IN):</label>
                                <input type="number" id="router-inputs" placeholder="96" value="96" min="1" max="1024">
                            </div>
                            <div class="form-row">
                                <label for="router-outputs">Router Size - Outputs (OUT):</label>
                                <input type="number" id="router-outputs" placeholder="384" value="384" min="1" max="1024">
                            </div>
                            <div class="protocol-info">
                                <h4>Quartz Protocol</h4>
                                <p>Currently implemented protocol for Evertz Quartz routers.</p>
                                <div class="protocol-commands">
                                    <div class="command-item">
                                        <span class="command-name">Take:</span>
                                        <code>.SV{destination},{source}</code>
                                    </div>
                                    <div class="command-item">
                                        <span class="command-name">Lock:</span>
                                        <code>.BL{destination}</code>
                                    </div>
                                    <div class="command-item">
                                        <span class="command-name">Unlock:</span>
                                        <code>.BU{destination}</code>
                                    </div>
                                    <div class="command-item">
                                        <span class="command-name">Query Status:</span>
                                        <code>.IV{destination}</code>
                                    </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <button id="save-router-config" class="action-button">Save Configuration</button>
                            </div>
                        </div>
                        <div class="router-section-container">
                            <div class="router-section-title">Application Settings</div>
                            <div class="form-row">
                                <label for="auto-connect">Auto Connect on Startup:</label>
                                <input type="checkbox" id="auto-connect">
                            </div>
                            <div class="form-row">
                                <label for="default-tab">Default Tab:</label>
                                <select id="default-tab">
                                    <option value="setup">Setup</option>
                                    <option value="router-control" selected>Router Control</option>
                                    <option value="category-management">Category</option>
                                    <option value="monitor-wall">Monitor Wall</option>
                                </select>
                            </div>
                            <div class="form-row">
                                <button id="save-app-settings" class="action-button">Save Settings</button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!-- Presets Tab -->
        <div id="presets" class="tab-content">
            <div class="panel-section">
                <h2 class="current-state-title">Crosspoint Presets</h2>



                <div class="control-panel">
                    <!-- Two Column Layout -->
                    <div style="display: flex; gap: 20px; height: 100%;">
                        <!-- Left Column - Controls (33%) -->
                        <div style="flex: 0 0 33%; display: flex; flex-direction: column; gap: 20px;">
                            <!-- Save Section -->
                            <div class="router-section-container" style="border: 2px solid #28a745; border-radius: 8px;">
                                <div class="router-section-title" style="background-color: #28a745; color: white; margin: -1px -1px 15px -1px; padding: 12px; border-radius: 6px 6px 0 0;">
                                    💾 Save Current Routing State
                                </div>
                                <div class="form-row">
                                    <label for="preset-name">Preset Name:</label>
                                    <input type="text" id="preset-name" placeholder="e.g., Morning Show Setup, Live Event Config" style="flex: 1;">
                                </div>
                                <div class="form-row">
                                    <button id="save-preset-btn" class="action-button" style="background-color: #28a745; color: white; font-weight: bold; padding: 12px 24px; width: 100%;">
                                        💾 Save Current State
                                    </button>
                                </div>
                                <div style="margin-top: 10px;">
                                    <span id="save-preset-status" style="font-size: 12px; color: #6c757d; font-style: italic;"></span>
                                </div>
                            </div>

                            <!-- Recall Section -->
                            <div class="router-section-container" style="border: 2px solid #007bff; border-radius: 8px;">
                                <div class="router-section-title" style="background-color: #007bff; color: white; margin: -1px -1px 15px -1px; padding: 12px; border-radius: 6px 6px 0 0;">
                                    🔄 Recall Saved Preset
                                </div>
                                <div class="form-row">
                                    <label for="preset-select">Saved Presets:</label>
                                    <select id="preset-select" style="flex: 1;">
                                        <option value="">-- Select a preset to recall --</option>
                                        <!-- Will be populated dynamically -->
                                    </select>
                                </div>
                                <div class="form-row">
                                    <label for="respect-locks-checkbox" style="display: flex; align-items: center;">
                                        <input type="checkbox" id="respect-locks-checkbox" checked style="margin-right: 8px;">
                                        <span style="font-size: 13px;">Respect Locks (skip locked outputs)</span>
                                    </label>
                                </div>
                                <div class="form-row" style="gap: 10px;">
                                    <button id="recall-preset-btn" class="action-button" style="background-color: #007bff; color: white; font-weight: bold; padding: 10px 16px; flex: 1;">
                                        🔄 Recall
                                    </button>
                                    <button id="delete-preset-btn" class="action-button" style="background-color: #dc3545; color: white; padding: 10px 16px; flex: 1;">
                                        🗑️ Delete
                                    </button>
                                </div>
                                <div id="preset-details" style="margin-top: 15px; padding: 12px; background-color: #f8f9fa; border-radius: 6px; font-size: 12px; display: none; border-left: 3px solid #007bff;">
                                    <!-- Preset details will be shown here -->
                                </div>
                            </div>

                            <!-- Import/Export Section -->
                            <div class="router-section-container" style="border: 2px solid #6c757d; border-radius: 8px;">
                                <div class="router-section-title" style="background-color: #6c757d; color: white; margin: -1px -1px 15px -1px; padding: 12px; border-radius: 6px 6px 0 0;">
                                    📁 Import/Export Presets
                                </div>
                                <div class="form-row" style="gap: 10px;">
                                    <button id="export-presets-btn" class="action-button" style="padding: 8px 12px; flex: 1; font-size: 13px;">📤 Export</button>
                                    <button id="import-presets-btn" class="action-button" style="padding: 8px 12px; flex: 1; font-size: 13px;">📥 Import</button>
                                    <input type="file" id="import-presets-file" style="display:none" accept="application/json">
                                </div>
                            </div>

                            <!-- Protocol Info Section - At the bottom -->
                            <div class="router-section-container" style="border: 2px solid #007bff; border-radius: 8px; margin-top: auto;">
                                <div class="router-section-title" style="background-color: #007bff; color: white; margin: -1px -1px 15px -1px; padding: 12px; border-radius: 6px 6px 0 0; font-weight: bold;">
                                    🎛️ Manual Salvo System
                                </div>
                                <div style="padding: 15px;">
                                    <p style="margin: 0 0 15px 0; color: #495057; line-height: 1.5; font-size: 13px;">
                                        Universal crosspoint preset system that works with any router protocol.
                                        Saves complete routing state and recalls it output-by-output with detailed progress logging.
                                    </p>
                                    <div id="protocol-compatibility-info" style="padding: 12px; background-color: #fff3cd; border-radius: 6px; border-left: 4px solid #ffc107;">
                                        <!-- Will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column - Operation Log (66%) -->
                        <div style="flex: 0 0 66%;">
                            <div class="router-section-container" style="height: 100%; display: flex; flex-direction: column;">
                                <div class="router-section-title" style="background-color: #17a2b8; color: white; padding: 12px; border-radius: 6px 6px 0 0; margin-bottom: 0; flex-shrink: 0;">
                                    📋 Operation Log
                                </div>
                                <div id="preset-log-section" style="flex: 1; display: flex; flex-direction: column; border: 2px solid #17a2b8; border-top: none; border-radius: 0 0 6px 6px; background-color: #f8f9fa;">
                                    <!-- Progress Bar Section inside Log -->
                                    <div id="preset-progress" style="display: none; padding: 15px; background-color: #fff3cd; border-bottom: 1px solid #dee2e6; flex-shrink: 0;">
                                        <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                            <span style="font-size: 16px; margin-right: 8px;">⚡</span>
                                            <div style="font-weight: bold; font-size: 14px; color: #856404;" id="preset-operation-text">Processing...</div>
                                        </div>
                                        <div style="width: 100%; background-color: #e0e0e0; border-radius: 6px; height: 28px; overflow: hidden; margin-bottom: 8px; border: 1px solid #dee2e6;">
                                            <div id="preset-progress-bar" style="width: 0%; height: 100%; background: linear-gradient(90deg, #28a745 0%, #20c997 100%); transition: width 0.3s ease; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 12px;"></div>
                                        </div>
                                        <div id="preset-progress-text" style="text-align: center; font-size: 14px; font-weight: bold; color: #856404;">0%</div>
                                    </div>

                                    <div id="preset-log" style="flex: 1; overflow-y: auto; padding: 15px; font-family: 'Courier New', monospace; font-size: 12px; line-height: 1.4; min-height: 400px;">
                                        <div style="color: #6c757d; font-style: italic; text-align: center; margin-top: 50px;">
                                            Operation log will appear here when you save or recall presets...
                                        </div>
                                    </div>
                                    <div style="padding: 10px 15px; border-top: 1px solid #dee2e6; background-color: #ffffff; border-radius: 0 0 4px 4px; flex-shrink: 0;">
                                        <button id="clear-log-btn" class="action-button" style="background-color: #6c757d; color: white; padding: 6px 12px; font-size: 12px; float: right;">Clear Log</button>
                                        <div style="clear: both;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Router Control Tab -->
        <div id="router-control" class="tab-content active">
            <div class="status-bar">
                <div class="status-indicator">
                    <div id="status-dot" class="status-dot status-disconnected"></div>
                    <div id="status-text">Disconnected</div>
                </div>
                <div>
                    <span>Router: *************:4000</span>
                    <div class="connect-buttons">
                        <button id="connect-btn" class="connect-button connect">Connect</button>
                        <button id="disconnect-btn" class="connect-button disconnect">Disconnect</button>
                    </div>
                </div>
            </div>

            <div class="control-panel">
                <div class="side-by-side-panel">
                    <div class="panel-section current-state-panel">
                        <h2 class="current-state-title">Current State</h2>
                        <div class="status-window">
                            <div class="status-label">Current Output:</div>
                            <div id="selected-output-name">Output 1</div>
                        </div>
                        <div id="input-status-window" class="status-window">
                            <div class="status-label">Routed Input:</div>
                            <div id="output-source">Unknown</div>
                            <div id="lock-text" class="locked-text">LOCKED</div>
                        </div>
                        <div class="button-row" style="align-items: flex-start;">
                            <button id="refresh-btn" class="refresh">Refresh Status</button>
                            <button id="lock-btn" class="lock">LOCK</button>
                            <button id="take-btn" class="take" disabled>TAKE</button>
                        </div>
                    </div>
                    <div class="panel-section log-panel-section">
                        <h2 class="log-title">Log</h2>
                        <div id="log-panel" class="log-panel"></div>
                    </div>
                </div>

                <div class="router-section-row">
                    <div class="router-section-container">
                        <div class="router-section-title">Sources</div>
                        <div class="category-buttons" id="source-categories">
                            <button class="category-button" data-category="ALL">ALL</button>
                            <button class="category-button" data-category="CCU">CCU</button>
                            <button class="category-button" data-category="CCU_CHAR">CCU CHAR</button>
                            <button class="category-button" data-category="CCU_SSL">CCU SSL</button>
                            <button class="category-button" data-category="SWITCHER">SWITCHER</button>
                            <button class="category-button" data-category="MUX">MUX</button>
                            <button class="category-button" data-category="EXT">EXT</button>
                            <button class="category-button" data-category="EVS_1">EVS 1</button>
                            <button class="category-button" data-category="EVS_2">EVS 2</button>
                            <button class="category-button" data-category="EVS_3">EVS 3</button>
                            <button class="category-button" data-category="VTR">VTR</button>
                            <button class="category-button" data-category="WF">WF</button>
                            <button class="category-button" data-category="PC_OUT">PC OUT</button>
                        </div>
                        <div id="source-buttons" class="square-button-container">
                            <!-- Source buttons will be generated here -->
                        </div>
                        <select id="source-select">
                            <option value="">-- Select Source --</option>
                            <!-- Will be populated from JavaScript -->
                        </select>
                    </div>
                    <div class="router-section-container">
                        <div class="router-section-title">Destinations</div>
                        <div class="category-buttons" id="destination-categories">
                            <button class="category-button" data-category="ALL">ALL</button>
                            <button class="category-button" data-category="SWITCHER">SWITCHER</button>
                            <button class="category-button" data-category="MUX">MUX</button>
                            <button class="category-button" data-category="LINES">LINES</button>
                            <button class="category-button" data-category="EVS1">EVS1</button>
                            <button class="category-button" data-category="EVS2">EVS2</button>
                            <button class="category-button" data-category="CAR_DDA">CAR DDA</button>
                            <button class="category-button" data-category="WF">WF</button>
                            <button class="category-button" data-category="SLM">SLM</button>
                            <button class="category-button" data-category="RVM">RVM</button>
                            <button class="category-button" data-category="VTR">VTR</button>
                            <button class="category-button" data-category="CCU_RET">CCU RET</button>
                            <button class="category-button" data-category="PC_IN">PC IN</button>
                        </div>
                        <div id="destination-buttons" class="square-button-container">
                            <!-- Destination buttons will be generated here -->
                        </div>
                        <select id="destination-select">
                            <option value="">-- Select Destination --</option>
                            <!-- Will be populated from JavaScript -->
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Category Management Tab -->
        <div id="category-management" class="tab-content">
            <div class="panel-section">
                <h2 class="current-state-title">Category Management</h2>

                <!-- Create/Edit Category Form - Hidden by default -->
                <div id="category-form-container" class="category-form" style="display: none;">
                    <div class="router-section-container" style="border: 2px solid #28a745; border-radius: 8px;">
                        <div class="router-section-title" style="background-color: #28a745; color: white; margin: -1px -1px 15px -1px; padding: 12px; border-radius: 6px 6px 0 0;">
                            <span id="form-title-text">📝 Create New Category</span>
                        </div>

                        <div style="padding: 20px;">
                            <div class="form-row">
                                <label for="category-type">Category Type:</label>
                                <select id="category-type">
                                    <option value="destination">Destination (Output)</option>
                                    <option value="source">Source (Input)</option>
                                </select>
                            </div>

                            <div class="form-row">
                                <label for="category-name">Category Name:</label>
                                <input type="text" id="category-name" placeholder="Enter category name">
                            </div>

                            <div class="form-row">
                                <label id="ios-selector-label">Select I/Os for this category:</label>
                                <div class="ios-selector" id="ios-selector">
                                    <!-- Will be populated dynamically -->
                                </div>
                            </div>

                            <div class="action-buttons" style="margin-top: 20px;">
                                <button id="save-category-btn" class="action-button" style="background-color: #28a745; color: white;">💾 Save Category</button>
                                <button id="cancel-category-btn" class="action-button" style="background-color: #6c757d; color: white; margin-left: 10px;">❌ Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Category Lists -->
                <div class="control-panel">
                    <div style="display: flex; gap: 20px;">
                        <!-- Destination Categories Column -->
                        <div style="flex: 1;">
                            <div class="router-section-container">
                                <div class="router-section-title" style="background-color: #007bff; color: white;">
                                    📤 Destination Categories
                                    <button id="add-destination-category-btn" class="action-button" style="float: right; background-color: rgba(255,255,255,0.2); color: white; padding: 4px 8px; font-size: 12px; margin-top: -2px;">
                                        ➕ Add New
                                    </button>
                                </div>
                                <div id="destination-category-list" class="category-list-new">
                                    <!-- Will be populated dynamically -->
                                </div>
                            </div>
                        </div>

                        <!-- Source Categories Column -->
                        <div style="flex: 1;">
                            <div class="router-section-container">
                                <div class="router-section-title" style="background-color: #17a2b8; color: white;">
                                    📥 Source Categories
                                    <button id="add-source-category-btn" class="action-button" style="float: right; background-color: rgba(255,255,255,0.2); color: white; padding: 4px 8px; font-size: 12px; margin-top: -2px;">
                                        ➕ Add New
                                    </button>
                                </div>
                                <div id="source-category-list" class="category-list-new">
                                    <!-- Will be populated dynamically -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monitors Tab -->
        <div id="monitors" class="tab-content">
            <div class="panel-section">
                <h2>Monitors Control Panel</h2>
                <div class="monitors-container">
                    <div class="monitors-toolbar">
                        <button id="create-monitor-btn" class="action-button">Create Monitor</button>
                        <button id="copy-button-btn" class="action-button">Copy</button>
                        <button id="paste-button-btn" class="action-button">Paste</button>
                        <button id="save-ui-btn" class="action-button">Save UI</button>
                        <button id="load-ui-btn" class="action-button">Load UI</button>
                        <div class="size-controls">
                            <div class="size-control">
                                <span>Width:</span>
                                <input type="range" id="button-width-slider" min="50" max="200" value="100">
                                <span id="button-width-value">100px</span>
                            </div>
                            <div class="size-control">
                                <span>Height:</span>
                                <input type="range" id="button-height-slider" min="50" max="200" value="100">
                                <span id="button-height-value">100px</span>
                            </div>
                        </div>
                    </div>
                    <div id="monitor-board" class="monitor-board"></div>
                    <div id="assignment-controls" style="display: none;">
                        <select id="assignment-type">
                            <option value="destination">Destination</option>
                            <option value="source">Source</option>
                        </select>
                        <select id="assignment-options"></select>
                        <button id="start-assignment-btn" class="action-button">Assign</button>
                        <button id="cancel-assignment-btn" class="action-button">Cancel</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Names Tab -->
        <div id="names" class="tab-content">
            <div class="panel-section">
                <h2 class="current-state-title">Router Names</h2>

                <!-- Add/Edit Names Container - Top -->
                <div class="names-edit-container">
                    <div class="router-section-container">
                        <div class="router-section-title">Add/Edit Names</div>
                        <div class="names-edit-form">
                            <div class="form-row">
                                <label for="name-type">Type:</label>
                                <select id="name-type">
                                    <option value="in">Input (IN)</option>
                                    <option value="out">Output (OUT)</option>
                                </select>
                            </div>
                            <div class="form-row">
                                <label for="name-number">Number:</label>
                                <input type="number" id="name-number" min="1" max="1024" value="1">
                            </div>
                            <div class="form-row">
                                <label for="router-name">Router Name:</label>
                                <input type="text" id="router-name" placeholder="Enter router name">
                            </div>
                            <div class="form-row">
                                <label for="switcher-name">Switcher Name:</label>
                                <input type="text" id="switcher-name" placeholder="Enter switcher name (future use)">
                            </div>
                            <div class="form-row">
                                <label for="multiviewer-name">Multiviewer Name:</label>
                                <input type="text" id="multiviewer-name" placeholder="Enter multiviewer name (future use)">
                            </div>
                            <div class="form-row">
                                <button id="add-name-btn" class="action-button">Add/Update Name</button>
                                <button id="clear-name-btn" class="action-button" style="margin-left: 10px; background-color: #6c757d; color: white;">Clear Form</button>
                                <button id="delete-name-btn" class="action-button" style="margin-left: 10px; background-color: #dc3545; color: white;">Delete Name</button>
                                <button id="export-names-btn" class="action-button" style="margin-left: 10px;">Export Names</button>
                                <button id="import-names-btn" class="action-button" style="margin-left: 10px;">Import Names</button>
                                <input type="file" id="import-names-file" style="display:none" accept="application/json">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Names Lists Container - Bottom (Two Columns) -->
                <div class="names-lists-container">
                    <!-- Left Column - Inputs -->
                    <div class="names-column">
                        <div class="names-column-header">
                            <h3 class="section-header">INPUTS</h3>
                            <div class="names-column-controls">
                                <input type="text" id="inputs-search" placeholder="Search inputs..." class="names-search-input">
                                <span id="inputs-count" class="names-count">0 inputs</span>
                            </div>
                        </div>
                        <div class="names-list-container">
                            <div id="inputs-list" class="names-list">
                                <!-- Will be populated dynamically -->
                            </div>
                        </div>
                    </div>

                    <!-- Right Column - Outputs -->
                    <div class="names-column">
                        <div class="names-column-header">
                            <h3 class="section-header">OUTPUTS</h3>
                            <div class="names-column-controls">
                                <input type="text" id="outputs-search" placeholder="Search outputs..." class="names-search-input">
                                <span id="outputs-count" class="names-count">0 outputs</span>
                            </div>
                        </div>
                        <div class="names-list-container">
                            <div id="outputs-list" class="names-list">
                                <!-- Will be populated dynamically -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monitor Wall Tab -->
        <div id="monitor-wall" class="tab-content">
            <div class="panel-section">
                <h2 class="monitor-wall-title">Monitor Wall</h2>
                <div class="monitors-container">
                    <div class="monitors-toolbar">
                        <button id="create-wall-window-btn" class="action-button">Create Window</button>
                        <button id="lock-wall-btn" class="action-button">Lock Windows</button>
                        <button id="copy-wall-size-btn" class="action-button">Copy</button>
                        <button id="paste-wall-size-btn" class="action-button">Paste</button>
                        <button id="save-all-btn" class="action-button">Save All</button>
                        <button id="load-all-btn" class="action-button">Load All</button>
                        <button id="export-all-btn" class="action-button">Export All</button>
                        <button id="import-all-btn" class="action-button">Import All</button>
                        <input type="file" id="import-all-file" style="display:none" accept="application/json">
                        <label style="margin-left:16px;">Columns: <input type="number" id="wall-cols-input" min="1" max="20" value="6" style="width:50px;"></label>
                        <label style="margin-left:8px;">Rows: <input type="number" id="wall-rows-input" min="1" max="20" value="4" style="width:50px;"></label>
                        <div class="size-controls">
                            <div class="size-control">
                                <span>Width:</span>
                                <input type="range" id="wall-window-width-slider" min="50" max="400" value="200">
                                <span id="wall-window-width-value">200px</span>
                            </div>
                            <div class="size-control">
                                <span>Height:</span>
                                <input type="range" id="wall-window-height-slider" min="50" max="400" value="150">
                                <span id="wall-window-height-value">150px</span>
                            </div>
                        </div>
                    </div>
                    <div id="monitor-wall-board" class="monitor-board" style="position: relative;">
                        <div id="wall-board-resize-handle"></div>
                    </div>
                    <div style="margin-top: 0;">
                        <div class="panel-section" style="margin-top: 0;">
                            <div class="routing-container">
                                <div class="routing-column">
                                    <div class="section-header" style="position: relative; display: flex; align-items: center; justify-content: center; gap: 16px;">
                                        <span class="sources-title">Sources</span>
                                        <button id="wall-take-btn" class="take" disabled style="margin-left: 16px; position: static;">TAKE</button>
                                        <label style="margin-left:10px; display:flex; align-items:center; font-size:14px;">
                                            <input type="checkbox" id="wall-take-confirm-checkbox" checked style="margin-right:4px;">
                                            Active Take
                                        </label>
                                    </div>
                                    <div class="category-buttons" id="wall-source-categories"></div>
                                    <div id="wall-source-buttons" class="square-button-container"></div>
                                    <select id="wall-source-select">
                                        <option value="">-- Select Source --</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Connection Modal -->
    <div id="connection-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🔌 Router Connection</h2>
            </div>
            <div class="modal-body">
                <p style="margin-bottom: 20px; color: #495057; line-height: 1.5;">
                    Welcome to Router Control Application! You can either connect to your router now or continue working offline.
                </p>
                <div class="connection-info">
                    <div class="info-row">
                        <span class="info-label">Router Address:</span>
                        <span id="modal-router-address" class="info-value">***********:5000</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Protocol:</span>
                        <span id="modal-router-protocol" class="info-value">RCP3 (Utah)</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Router Size:</span>
                        <span id="modal-router-size" class="info-value">96 inputs × 96 outputs</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="modal-connect-btn" class="modal-button connect-btn">
                    🔌 Connect to Router
                </button>
                <button id="modal-offline-btn" class="modal-button offline-btn">
                    📱 Continue Offline
                </button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
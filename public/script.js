document.addEventListener('DOMContentLoaded', () => {
    // Elements
    const statusDot = document.getElementById('status-dot');
    const statusText = document.getElementById('status-text');
    const outputSource = document.getElementById('output-source');
    const selectedOutputName = document.getElementById('selected-output-name');
    const lockIndicator = document.getElementById('lock-indicator');
    const refreshBtn = document.getElementById('refresh-btn');
    const destinationSelect = document.getElementById('destination-select');
    const sourceSelect = document.getElementById('source-select');
    const takeBtn = document.getElementById('take-btn');
    const lockBtn = document.getElementById('lock-btn');
    const logPanel = document.getElementById('log-panel');
    const destinationCategories = document.getElementById('destination-categories');
    const sourceCategories = document.getElementById('source-categories');
    const connectBtn = document.getElementById('connect-btn');
    const disconnectBtn = document.getElementById('disconnect-btn');
    const inputStatusWindow = document.getElementById('input-status-window');
    const lockText = document.getElementById('lock-text');
    const destinationButtons = document.getElementById('destination-buttons');
    const sourceButtons = document.getElementById('source-buttons');

    // Setup Tab Elements
    const routerIpInput = document.getElementById('router-ip');
    const routerPortInput = document.getElementById('router-port');
    const saveRouterConfigBtn = document.getElementById('save-router-config');
    const autoConnectCheckbox = document.getElementById('auto-connect');
    const defaultTabSelect = document.getElementById('default-tab');
    const saveAppSettingsBtn = document.getElementById('save-app-settings');
    const routerProtocolSelect = document.getElementById('router-protocol');
    const saveProtocolConfigBtn = document.getElementById('save-protocol-config');
    const protocolInfo = document.querySelector('.protocol-info');

    // Tab Navigation Elements
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    // Category Management Elements
    const categoryType = document.getElementById('category-type');
    const categoryNameInput = document.getElementById('category-name');
    const iosSelector = document.getElementById('ios-selector');
    const saveCategoryBtn = document.getElementById('save-category-btn');
    const cancelCategoryBtn = document.getElementById('cancel-category-btn');
    const destinationCategoryList = document.getElementById('destination-category-list');
    const sourceCategoryList = document.getElementById('source-category-list');
    const addDestinationCategoryBtn = document.getElementById('add-destination-category-btn');
    const addSourceCategoryBtn = document.getElementById('add-source-category-btn');

    // Monitors Tab Elements
    const monitorBoard = document.getElementById('monitor-board');
    const createMonitorBtn = document.getElementById('create-monitor-btn');
    const assignmentTypeSelect = document.getElementById('assignment-type');
    const startAssignmentBtn = document.getElementById('start-assignment-btn');
    const cancelAssignmentBtn = document.getElementById('cancel-assignment-btn');

    // Monitor Wall Tab Elements
    const monitorWallBoard = document.getElementById('monitor-wall-board');
    const createWallWindowBtn = document.getElementById('create-wall-window-btn');
    const lockWallBtn = document.getElementById('lock-wall-btn');
    const wallWindowWidthSlider = document.getElementById('wall-window-width-slider');
    const wallWindowWidthValue = document.getElementById('wall-window-width-value');
    const wallWindowHeightSlider = document.getElementById('wall-window-height-slider');
    const wallWindowHeightValue = document.getElementById('wall-window-height-value');
    const wallSourceCategories = document.getElementById('wall-source-categories');
    const wallSourceButtons = document.getElementById('wall-source-buttons');
    const wallSourceSelect = document.getElementById('wall-source-select');
    const wallTakeBtn = document.getElementById('wall-take-btn');
    const wallBoardResizeHandle = document.getElementById('wall-board-resize-handle');
    const wallTakeConfirmCheckbox = document.getElementById('wall-take-confirm-checkbox');

    // Monitor Wall Grid Size Elements
    const wallColsInput = document.getElementById('wall-cols-input');
    const wallRowsInput = document.getElementById('wall-rows-input');
    const DEFAULT_CELL_SIZE = 200;

    // State
    let wsConnection = null;
    let selectedOutput = 1; // Default to output 1
    let selectedInput = null;
    let sourceNames = {};
    let destinationNames = {};
    let outputStates = {}; // Track current state of each output
    // These will be updated from routerConfig
    let maxInputs = 96;
    let maxOutputs = 384;
    let isLocked = false; // Track lock status of selected output
    let selectedDestCategory = 'ALL'; // Default destination category
    let selectedSrcCategory = 'ALL'; // Default source category
    let editingCategoryId = null; // Track which category is being edited
    let selectedDestType = 'built-in'; // Type of selected destination category
    let selectedSrcType = 'built-in'; // Type of selected source category
    let monitorButtons = []; // Array to track all monitor buttons
    let selectedMonitorButton = null; // Currently selected monitor button
    let isAssignmentMode = false; // Are we in assignment mode?
    let assignmentType = null; // 'destination' or 'source'
    let copiedButtonProperties = null; // Store copied button properties for paste functionality

    // Router configuration
    let routerConfig = {
        host: '*************',
        port: 4000,
        inputs: 96,  // Default number of inputs
        outputs: 384 // Default number of outputs
    };

    // App settings
    let appSettings = {
        autoConnect: true,
        defaultTab: 'router-control'
    };

    // Protocol configuration
    let protocolConfig = {
        type: 'quartz'
    };

    // Protocol command mappings
    const protocolCommands = {
        quartz: {
            take: (destination, source) => `.SV${destination},${source}`,
            lock: (destination) => `.BL${destination}`,
            unlock: (destination) => `.BU${destination}`,
            queryStatus: (destination) => `.IV${destination}`,
            queryLock: (destination) => `.BI${destination}`,
            querySourceName: (source) => `.RS${source}`,
            queryDestinationName: (destination) => `.RD${destination}`,
            setSourceName: (source, name) => `.NS${source},${name}`,
            setDestinationName: (destination, name) => `.ND${destination},${name}`
        },
        rcp3: {
            // RCP3 protocol uses a packet-based format
            // For simplicity, we'll use a JSON-based representation that the server will convert to the proper binary format
            take: (destination, source) => JSON.stringify({
                command: 'take',
                destination: parseInt(destination),
                source: parseInt(source),
                levels: 1 // Default to level 1 (video)
            }),
            lock: (destination) => JSON.stringify({
                command: 'setLock',
                destination: parseInt(destination),
                lockType: 1, // 1B lock
                levels: 1 // Default to level 1 (video)
            }),
            unlock: (destination) => JSON.stringify({
                command: 'clearLock',
                destination: parseInt(destination),
                levels: 1 // Default to level 1 (video)
            }),
            queryStatus: (destination) => JSON.stringify({
                command: 'getMatrix',
                startDest: parseInt(destination),
                numDests: 1
            }),
            queryLock: (destination) => JSON.stringify({
                command: 'getLock',
                startDest: parseInt(destination),
                numDests: 1
            }),
            querySourceName: (source) => JSON.stringify({
                command: 'getSourceName',
                source: parseInt(source)
            }),
            queryDestinationName: (destination) => JSON.stringify({
                command: 'getDestinationName',
                destination: parseInt(destination)
            }),
            setSourceName: (source, name) => JSON.stringify({
                command: 'setSourceName',
                source: parseInt(source),
                name: name
            }),
            setDestinationName: (destination, name) => JSON.stringify({
                command: 'setDestinationName',
                destination: parseInt(destination),
                name: name
            })
        }
    };

    // Names Tab State
    let routerNames = {
        inputs: {},  // { number: { routerName, switcherName, multiviewerName } }
        outputs: {}  // { number: { routerName, switcherName, multiviewerName } }
    };
    let selectedNameRow = null;

    // Monitor Wall State
    let wallWindows = [];
    let selectedWallWindow = null;
    let wallWindowsLocked = false;
    let selectedWallSource = null;
    let selectedWallCategory = 'ALL';

    // Special ALL category for showing all inputs/outputs
    const allDestinationRange = Array.from({length: maxOutputs}, (_, i) => i + 1);
    const allSourceRange = Array.from({length: maxInputs}, (_, i) => i + 1);

    // User-defined categories (stored in localStorage)
    let customCategories = loadCustomCategories();

    // Ensure we have ALL categories at minimum
    function ensureBasicCategories() {
        // Check if we have at least the ALL categories
        if (!customCategories.destination.find(cat => cat.name === 'ALL')) {
            customCategories.destination.push({
                id: 'all-dest',
                name: 'ALL',
                ios: allDestinationRange,
                isBuiltIn: true,
                isSpecial: true // Cannot be deleted
            });
        }

        if (!customCategories.source.find(cat => cat.name === 'ALL')) {
            customCategories.source.push({
                id: 'all-src',
                name: 'ALL',
                ios: allSourceRange,
                isBuiltIn: true,
                isSpecial: true // Cannot be deleted
            });
        }

        saveCustomCategories();
    }

    // WebSocket connection
    function connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}`;

        wsConnection = new WebSocket(wsUrl);

        wsConnection.onopen = () => {
            addLogEntry('WebSocket connection established', 'info');
        };

        wsConnection.onclose = () => {
            addLogEntry('WebSocket connection closed, reconnecting...', 'error');
            updateConnectionStatus('disconnected');

            // Try to reconnect after 3 seconds
            setTimeout(connectWebSocket, 3000);
        };

        wsConnection.onerror = (error) => {
            addLogEntry(`WebSocket error: ${error.message || 'Unknown error'}`, 'error');
        };

        wsConnection.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);

                if (data.type === 'connection') {
                    updateConnectionStatus(data.status, data.message);

                    if (data.status === 'connected') {
                        // Query current state once connected
                        queryRouterState();
                    }
                }
                else if (data.type === 'routerResponse') {
                    handleRouterResponse(data.message);
                }
                else if (data.type === 'routerCommand') {
                    addLogEntry(`Command sent: ${data.command}`, 'command');
                }
                else if (data.type === 'sourceNames') {
                    // Update source names
                    sourceNames = data.names;
                    populateSourceOptions();
                    updateOutputSourceDisplay();
                    updateMonitorButtonLabels();
                }
                else if (data.type === 'destinationNames') {
                    // Update destination names
                    destinationNames = data.names;
                    populateDestinationOptions();
                    updateSelectedOutputName();
                    updateMonitorButtonLabels();
                }
                else if (data.type === 'protocolUpdate') {
                    // Update protocol configuration
                    protocolConfig.type = data.protocol;
                    routerProtocolSelect.value = protocolConfig.type;
                    updateProtocolInfo();
                    addLogEntry(`Protocol updated to: ${data.protocol}`, 'info');
                }
                else if (data.type === 'nameQueryProgress') {
                    // Handle name query progress updates
                    handleNameQueryProgress(data);
                }
            } catch (error) {
                addLogEntry(`Error processing message: ${error.message}`, 'error');
            }
        };
    }

    // Connect to the router explicitly
    function connectToRouterManually() {
        if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
            // Use the router configuration from the Setup tab
            wsConnection.send(JSON.stringify({
                command: 'connectRouter',
                config: routerConfig
            }));
            addLogEntry(`Manual connect request sent to server (${routerConfig.host}:${routerConfig.port})`, 'command');
        } else {
            addLogEntry('Cannot send connect command, WebSocket not connected', 'error');
        }
    }

    // Disconnect from the router explicitly
    function disconnectFromRouterManually() {
        if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
            wsConnection.send(JSON.stringify({ command: 'disconnectRouter' }));
            addLogEntry('Manual disconnect request sent to server', 'command');
        } else {
            addLogEntry('Cannot send disconnect command, WebSocket not connected', 'error');
        }
    }

    // Populate source dropdown based on category
    function populateSourceOptions() {
        // Clear existing options except the first one
        while (sourceSelect.options.length > 1) {
            sourceSelect.remove(1);
        }

        // Get inputs for the selected category
        let inputs;

        if (selectedSrcCategory === 'ALL') {
            inputs = allSourceRange;
        } else {
            const category = customCategories.source.find(cat => cat.id === selectedSrcCategory);
            inputs = category ? category.ios : allSourceRange;
        }

        // Add options based on discovered sources
        const knownSources = Object.keys(sourceNames)
            .map(Number)
            .filter(num => inputs.includes(num))
            .sort((a, b) => a - b);

        knownSources.forEach(sourceNum => {
            const name = sourceNames[sourceNum];
            const option = document.createElement('option');
            option.value = sourceNum;
            option.textContent = `Input ${sourceNum}${name ? ': ' + name : ''}`;
            sourceSelect.appendChild(option);
        });

        // Add options for sources in the selected range that we don't have names for
        const remainingInputs = inputs.filter(num => !knownSources.includes(num));
        for (let sourceNum of remainingInputs) {
            const option = document.createElement('option');
            option.value = sourceNum;
            option.textContent = `Input ${sourceNum}`;
            sourceSelect.appendChild(option);
        }

        // Generate square buttons for sources
        createSourceButtons(inputs);

        // If we previously had a selected input, try to select it again if it's in this category
        if (selectedInput && inputs.includes(selectedInput)) {
            sourceSelect.value = selectedInput;
            highlightSelectedSourceButton(selectedInput);
        } else if (sourceSelect.options.length > 1) {
            // Otherwise select the first item in the list
            sourceSelect.selectedIndex = 1;
            selectedInput = parseInt(sourceSelect.value);
            highlightSelectedSourceButton(selectedInput);
        } else {
            selectedInput = null;
        }

        // Update TAKE button state
        updateTakeButton();
    }

    // Populate destination dropdown based on category
    function populateDestinationOptions() {
        // Clear existing options except the first one
        while (destinationSelect.options.length > 1) {
            destinationSelect.remove(1);
        }

        // Get outputs for the selected category
        let outputs;

        if (selectedDestCategory === 'ALL') {
            outputs = allDestinationRange;
        } else {
            const category = customCategories.destination.find(cat => cat.id === selectedDestCategory);
            outputs = category ? category.ios : allDestinationRange;
        }

        // Get discovered destinations (that we have names for)
        const knownDestinations = Object.keys(destinationNames)
            .map(Number)
            .filter(num => outputs.includes(num))
            .sort((a, b) => a - b);

        // Add options for destinations we have names for
        knownDestinations.forEach(destNum => {
            const name = destinationNames[destNum];
            const option = document.createElement('option');
            option.value = destNum;
            option.textContent = `Output ${destNum}${name ? ': ' + name : ''}`;
            destinationSelect.appendChild(option);
        });

        // Add options for destinations in the selected range that we don't have names for
        const remainingOutputs = outputs.filter(num => !knownDestinations.includes(num));
        for (let destNum of remainingOutputs) {
            const option = document.createElement('option');
            option.value = destNum;
            option.textContent = `Output ${destNum}`;
            destinationSelect.appendChild(option);
        }

        // Generate square buttons for destinations
        createDestinationButtons(outputs);

        // Try to select the current selected output if it's in this category
        if (outputs.includes(selectedOutput)) {
            destinationSelect.value = selectedOutput;
            highlightSelectedDestinationButton(selectedOutput);
        } else if (destinationSelect.options.length > 1) {
            // Otherwise select the first item in the list
            destinationSelect.selectedIndex = 1;
            selectedOutput = parseInt(destinationSelect.value);
            updateSelectedOutputName();
            queryOutputState(selectedOutput);
            highlightSelectedDestinationButton(selectedOutput);
        }
    }

    // Create square buttons for destinations
    function createDestinationButtons(outputs) {
        // Clear existing buttons
        destinationButtons.innerHTML = '';

        // Create buttons in rows of 8
        outputs.forEach(destNum => {
            const button = document.createElement('div');
            button.className = 'square-button output';
            button.dataset.output = destNum;

            // Get name if available
            let name = destinationNames[destNum] || `OUT ${destNum}`;

            // Format the name with line breaks for readability (max 8 chars per line)
            let formattedName = formatButtonText(name);

            // Display only the name, not the number
            button.innerHTML = `<span class="button-text">${formattedName}</span>`;

            // Highlight if this is the selected output
            if (destNum === selectedOutput) {
                button.classList.add('selected');
            }

            // Add click event
            button.addEventListener('click', () => {
                selectedOutput = destNum;
                updateSelectedOutputName();
                queryOutputState(selectedOutput);
                updateTakeButton();
                highlightSelectedDestinationButton(destNum);

                // Also update the dropdown to match
                destinationSelect.value = destNum;
            });

            destinationButtons.appendChild(button);
        });
    }

    // Create square buttons for sources
    function createSourceButtons(inputs) {
        // Clear existing buttons
        sourceButtons.innerHTML = '';

        // Create buttons in rows of 8
        inputs.forEach(sourceNum => {
            const button = document.createElement('div');
            button.className = 'square-button input';
            button.dataset.input = sourceNum;

            // Get name if available
            let name = sourceNames[sourceNum] || `IN ${sourceNum}`;

            // Format the name with line breaks for readability (max 8 chars per line)
            let formattedName = formatButtonText(name);

            // Display only the name, not the number
            button.innerHTML = `<span class="button-text">${formattedName}</span>`;

            // Highlight if this is the selected input
            if (sourceNum === selectedInput) {
                button.classList.add('selected');
            }

            // Add click event
            button.addEventListener('click', () => {
                selectedInput = sourceNum;
                updateTakeButton();
                highlightSelectedSourceButton(sourceNum);

                // Also update the dropdown to match
                sourceSelect.value = sourceNum;
            });

            sourceButtons.appendChild(button);
        });
    }

    // Helper function to format button text with line breaks (max 8 chars per line)
    function formatButtonText(text) {
        // If text is shorter than 8 characters, return as is
        if (text.length <= 8) return text;

        let result = '';
        let words = text.split(' ');
        let currentLine = '';

        // Process each word
        for (let i = 0; i < words.length; i++) {
            let word = words[i];

            // If word is too long, split it
            if (word.length > 8) {
                // If there's already something on current line, finish it first
                if (currentLine) {
                    result += currentLine + '<br>';
                    currentLine = '';
                }

                // Split long word into chunks of max 8 chars
                for (let j = 0; j < word.length; j += 8) {
                    let chunk = word.substr(j, 8);
                    result += chunk;

                    // Add line break if not the last chunk
                    if (j + 8 < word.length) {
                        result += '<br>';
                    }
                }
            }
            // If adding this word would exceed 8 chars on current line
            else if (currentLine.length + word.length + (currentLine ? 1 : 0) > 8) {
                result += currentLine + '<br>' + word;
                currentLine = '';
            }
            // Add word to current line
            else {
                currentLine = currentLine ? (currentLine + ' ' + word) : word;
            }
        }

        // Add any remaining text
        if (currentLine) {
            result += (result ? '<br>' : '') + currentLine;
        }

        return result;
    }

    // Highlight selected destination button
    function highlightSelectedDestinationButton(destNum) {
        // Remove 'selected' class from all buttons
        const buttons = destinationButtons.querySelectorAll('.square-button');
        buttons.forEach(btn => {
            btn.classList.remove('selected');
        });

        // Add 'selected' class to the selected button
        const selectedButton = destinationButtons.querySelector(`.square-button[data-output="${destNum}"]`);
        if (selectedButton) {
            selectedButton.classList.add('selected');
        }
    }

    // Highlight selected source button
    function highlightSelectedSourceButton(sourceNum) {
        // Remove 'selected' class from all buttons
        const buttons = sourceButtons.querySelectorAll('.square-button');
        buttons.forEach(btn => {
            btn.classList.remove('selected');
        });

        // Add 'selected' class to the selected button
        const selectedButton = sourceButtons.querySelector(`.square-button[data-input="${sourceNum}"]`);
        if (selectedButton) {
            selectedButton.classList.add('selected');
        }
    }

    // Update connection status display
    function updateConnectionStatus(status, message) {
        if (status === 'connected') {
            statusDot.className = 'status-dot status-connected';
            statusText.textContent = 'Connected';
            takeBtn.disabled = !selectedInput;
            connectBtn.disabled = true;
            disconnectBtn.disabled = false;
        } else if (status === 'failed') {
            statusDot.className = 'status-dot status-error';
            statusText.textContent = 'Connection Failed';
            takeBtn.disabled = true;
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;

            // Add a log entry for the connection failure
            addLogEntry('Connection failed after multiple attempts', 'error');
        } else {
            statusDot.className = 'status-dot status-disconnected';
            statusText.textContent = status === 'error' ? 'Connection Error' : 'Disconnected';
            takeBtn.disabled = true;
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;

            // If there's an error message, log it
            if (status === 'error' && message) {
                addLogEntry(`Connection error: ${message}`, 'error');
            }
        }
    }

    // Send command to router via WebSocket
    function sendRouterCommand(command) {
        if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
            wsConnection.send(JSON.stringify({ command }));
        } else {
            addLogEntry('Cannot send command, WebSocket not connected', 'error');
        }
    }

    // Get the appropriate command for the current protocol
    function getProtocolCommand(commandType, ...args) {
        const protocol = protocolConfig.type;
        if (protocolCommands[protocol] && protocolCommands[protocol][commandType]) {
            return protocolCommands[protocol][commandType](...args);
        } else {
            addLogEntry(`Command ${commandType} not supported for protocol ${protocol}`, 'error');
            return null;
        }
    }

    // Handle router responses
    function handleRouterResponse(message) {
        addLogEntry(`Response: ${message}`, 'response');

        // Update UI based on response
        if (message.startsWith('.UV')) {
            // Parse update message: .UV{dest},{srce}
            const matches = message.match(/\.UV(\d+),(\d+)/);
            if (matches && matches.length === 3) {
                const dest = parseInt(matches[1]);
                const srce = parseInt(matches[2]);

                // Store the current state of this output
                outputStates[dest] = { source: srce };

                // If this is the currently selected output, update display
                if (dest === selectedOutput) {
                    updateOutputSourceDisplay();
                }

                // Update wall windows
                wallWindows.forEach(w => {
                    if (w.destination === dest) updateWallWindowDOM(w);
                });
            }
        }
        else if (message.startsWith('.AV')) {
            // Parse interrogate response: .AV{dest},{srce}
            const matches = message.match(/\.AV(\d+),(\d+)/);
            if (matches && matches.length === 3) {
                const dest = parseInt(matches[1]);
                const srce = parseInt(matches[2]);

                // Store the current state of this output
                outputStates[dest] = outputStates[dest] || {};
                outputStates[dest].source = srce;

                // If this is the currently selected output, update display
                if (dest === selectedOutput) {
                    updateOutputSourceDisplay();
                }

                // Update wall windows
                wallWindows.forEach(w => {
                    if (w.destination === dest) updateWallWindowDOM(w);
                });
            }
        }
        else if (message.startsWith('.BA')) {
            // Parse lock status response: .BA{dest},{status}
            const matches = message.match(/\.BA(\d+),(\d+)/);
            if (matches && matches.length === 3) {
                const dest = parseInt(matches[1]);
                const status = parseInt(matches[2]);

                // Store the lock status of this output
                outputStates[dest] = outputStates[dest] || {};
                outputStates[dest].locked = status > 0;

                // If this is the currently selected output, update display
                if (dest === selectedOutput) {
                    updateLockStatus();
                    updateLockButton();
                }
            }
        }
    }

    // Update the display of the selected output's source
    function updateOutputSourceDisplay() {
        if (outputStates[selectedOutput] && outputStates[selectedOutput].source !== undefined) {
            const sourceNum = outputStates[selectedOutput].source;
            if (sourceNames[sourceNum]) {
                outputSource.textContent = `Input ${sourceNum} (${sourceNames[sourceNum]})`;
            } else {
                outputSource.textContent = `Input ${sourceNum}`;
            }
        } else {
            outputSource.textContent = 'Unknown';
        }
    }

    // Update lock indicator
    function updateLockStatus() {
        if (outputStates[selectedOutput] && outputStates[selectedOutput].locked) {
            lockText.style.display = 'inline'; // Show LOCKED text when locked
            isLocked = true;
            inputStatusWindow.classList.add('locked');
        } else {
            lockText.style.display = 'none'; // Hide LOCKED text when not locked
            isLocked = false;
            inputStatusWindow.classList.remove('locked');
        }
    }

    // Update the lock button state based on current lock status
    function updateLockButton() {
        if (isLocked) {
            lockBtn.textContent = 'UNLOCK';
            lockBtn.classList.add('active');
        } else {
            lockBtn.textContent = 'LOCK';
            lockBtn.classList.remove('active');
        }
    }

    // Update the displayed name of the selected output
    function updateSelectedOutputName() {
        const name = destinationNames[selectedOutput];
        if (name) {
            selectedOutputName.textContent = `Output ${selectedOutput}: ${name}`;
        } else {
            selectedOutputName.textContent = `Output ${selectedOutput}`;
        }
    }

    // Query router for information about a specific output
    function queryOutputState(outputNum) {
        // Query which input is connected to the output
        const queryStatusCmd = getProtocolCommand('queryStatus', outputNum);
        if (queryStatusCmd) sendRouterCommand(queryStatusCmd);

        // Query lock status of the output
        const queryLockCmd = getProtocolCommand('queryLock', outputNum);
        if (queryLockCmd) sendRouterCommand(queryLockCmd);

        // Query name of the output if we don't have it
        if (!destinationNames[outputNum]) {
            const queryDestNameCmd = getProtocolCommand('queryDestinationName', outputNum);
            if (queryDestNameCmd) sendRouterCommand(queryDestNameCmd);
        }
    }

    // Query all needed router information
    function queryRouterState() {
        // Query the currently selected output
        queryOutputState(selectedOutput);

        // Note: Name querying is now handled automatically by the server
        // based on router configuration (inputs/outputs from Setup tab)
        addLogEntry('Router state query initiated - names will be loaded automatically', 'info');
    }

    // Handle name query progress updates from server
    function handleNameQueryProgress(data) {
        switch (data.status) {
            case 'starting':
                addLogEntry(data.message, 'info');
                break;
            case 'querying_sources':
                if (data.current % 10 === 0 || data.current === data.total) {
                    addLogEntry(data.message, 'info');
                }
                break;
            case 'sources_complete':
                addLogEntry(data.message, 'success');
                break;
            case 'querying_destinations':
                if (data.current % 10 === 0 || data.current === data.total) {
                    addLogEntry(data.message, 'info');
                }
                break;
            case 'destinations_complete':
                addLogEntry(data.message, 'success');
                break;
            case 'complete':
                addLogEntry(data.message, 'success');
                // Refresh the UI to show all loaded names
                populateSourceOptions();
                populateDestinationOptions();
                updateOutputSourceDisplay();
                updateSelectedOutputName();
                break;
        }
    }

    // Add entry to log panel
    function addLogEntry(message, type = 'info') {
        const entry = document.createElement('div');
        entry.className = `log-entry ${type}`;

        const timestamp = new Date().toTimeString().split(' ')[0];
        entry.textContent = `[${timestamp}] ${message}`;

        logPanel.appendChild(entry);
        logPanel.scrollTop = logPanel.scrollHeight;
    }

    // --- Restore: Tab Navigation ---
    function initTabNavigation() {
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.getAttribute('data-tab');
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                button.classList.add('active');
                document.getElementById(tabId).classList.add('active');
                if (tabId === 'monitor-wall') {
                    renderWallSourceCategories();
                    renderWallSourceButtons();
                    wallWindows.forEach(w => updateWallWindowDOM(w));
                }
            });
        });
    }

    function initDestinationCategories() {
        const buttons = destinationCategories.querySelectorAll('.category-button');
        // Remove all active classes
        buttons.forEach(btn => btn.classList.remove('active'));
        // Set the default (ALL) button as active
        const defaultButton = destinationCategories.querySelector('[data-category="ALL"]');
        if (defaultButton) defaultButton.classList.add('active');
        buttons.forEach(button => {
            button.addEventListener('click', () => {
                buttons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                selectedDestCategory = button.getAttribute('data-category');
                populateDestinationOptions();
            });
        });
    }

    function initSourceCategories() {
        const buttons = sourceCategories.querySelectorAll('.category-button');
        // Remove all active classes
        buttons.forEach(btn => btn.classList.remove('active'));
        // Set the default (ALL) button as active
        const defaultButton = sourceCategories.querySelector('[data-category="ALL"]');
        if (defaultButton) defaultButton.classList.add('active');
        buttons.forEach(button => {
            button.addEventListener('click', () => {
                buttons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                selectedSrcCategory = button.getAttribute('data-category');
                populateSourceOptions();
            });
        });
    }

    // Category Management Functions
    function loadCustomCategories() {
        const savedCategories = localStorage.getItem('routerCustomCategories');
        return savedCategories ? JSON.parse(savedCategories) : {
            destination: [],
            source: []
        };
    }

    function saveCustomCategories() {
        localStorage.setItem('routerCustomCategories', JSON.stringify(customCategories));
        renderCategoryLists();
    }

    function showCategoryForm(type = 'destination', categoryId = null) {
        // Set the form to edit the specified category type
        categoryType.value = type;

        // If editing an existing category, populate the form
        if (categoryId !== null) {
            const category = customCategories[type].find(cat => cat.id === categoryId);
            if (category) {
                categoryNameInput.value = category.name;
                editingCategoryId = categoryId;
            }
        } else {
            // Clear form for new category
            categoryNameInput.value = '';
            editingCategoryId = null;
        }

        // Update the I/O selector based on category type
        updateIOSelector(type);

        // Show the category form
        document.querySelector('.category-form').style.display = 'block';
    }

    function updateIOSelector(type) {
        // Clear existing I/O options
        iosSelector.innerHTML = '';

        // Get the appropriate I/O range based on type
        const maxItems = type === 'destination' ? maxOutputs : maxInputs;
        const names = type === 'destination' ? destinationNames : sourceNames;

        // Create checkboxes for all I/Os
        for (let i = 1; i <= maxItems; i++) {
            const checkbox = document.createElement('div');
            checkbox.className = 'io-item';
            checkbox.dataset.io = i;

            // Use OUT/IN prefix instead of Output/Input
            const prefix = type === 'destination' ? 'OUT' : 'IN';
            const name = names[i] || `${prefix} ${i}`;
            checkbox.textContent = name;

            // If editing, check the box if this I/O is in the category
            if (editingCategoryId !== null) {
                const category = customCategories[type].find(cat => cat.id === editingCategoryId);
                if (category && category.ios.includes(i)) {
                    checkbox.classList.add('selected');
                }
            }

            // Toggle selected state on click
            checkbox.addEventListener('click', () => {
                checkbox.classList.toggle('selected');
            });

            iosSelector.appendChild(checkbox);
        }
    }

    function saveCategoryHandler() {
        const type = categoryType.value;
        const name = categoryNameInput.value.trim();

        // Validation
        if (name === '') {
            alert('Please enter a category name');
            return;
        }

        // Get selected I/Os
        const selectedIOs = Array.from(iosSelector.querySelectorAll('.io-item.selected'))
            .map(item => parseInt(item.dataset.io));

        if (selectedIOs.length === 0) {
            alert('Please select at least one I/O for this category');
            return;
        }

        // If editing, update existing category
        if (editingCategoryId !== null) {
            const categoryIndex = customCategories[type].findIndex(cat => cat.id === editingCategoryId);
            if (categoryIndex !== -1) {
                customCategories[type][categoryIndex] = {
                    id: editingCategoryId,
                    name: name,
                    ios: selectedIOs
                };
            }
        } else {
            // Add new category
            const newId = Date.now().toString();
            customCategories[type].push({
                id: newId,
                name: name,
                ios: selectedIOs
            });
        }

        // Save to localStorage and update UI
        saveCustomCategories();

        // Hide the form
        document.querySelector('.category-form').style.display = 'none';

        // Update category buttons in the main interface
        updateCategoryButtons();
    }

    function deleteCategoryHandler(type, categoryId) {
        const category = customCategories[type].find(cat => cat.id === categoryId);

        // Don't allow deletion of special categories like ALL
        if (category && category.isSpecial) {
            alert('This is a system category and cannot be deleted.');
            return;
        }

        if (confirm('Are you sure you want to delete this category?')) {
            const categoryIndex = customCategories[type].findIndex(cat => cat.id === categoryId);
            if (categoryIndex !== -1) {
                customCategories[type].splice(categoryIndex, 1);
                saveCustomCategories();
                updateCategoryButtons();
            }
        }
    }

    // Update category buttons in main interface
    function updateCategoryButtons() {
        // Update destination category buttons
        const destContainer = destinationCategories;
        destContainer.innerHTML = ''; // Clear all buttons

        // Add destination category buttons
        customCategories.destination.forEach(category => {
            const button = document.createElement('button');
            button.className = 'category-button';
            if (category.isBuiltIn) button.classList.add('built-in');
            button.dataset.category = category.id;
            button.dataset.type = 'custom';
            button.textContent = category.name;

            button.addEventListener('click', () => {
                // Remove active class from all buttons
                destContainer.querySelectorAll('.category-button').forEach(btn => {
                    btn.classList.remove('active');
                });

                // Add active class to this button
                button.classList.add('active');

                // Update selected category and repopulate
                selectedDestCategory = category.id;
                selectedDestType = 'custom';
                populateDestinationOptions();
            });

            // Set ALL as active by default
            if (category.name === 'ALL') {
                button.classList.add('active');
            }

            destContainer.appendChild(button);
        });

        // Update source category buttons
        const srcContainer = sourceCategories;
        srcContainer.innerHTML = ''; // Clear all buttons

        // Add source category buttons
        customCategories.source.forEach(category => {
            const button = document.createElement('button');
            button.className = 'category-button';
            if (category.isBuiltIn) button.classList.add('built-in');
            button.dataset.category = category.id;
            button.dataset.type = 'custom';
            button.textContent = category.name;

            button.addEventListener('click', () => {
                // Remove active class from all buttons
                srcContainer.querySelectorAll('.category-button').forEach(btn => {
                    btn.classList.remove('active');
                });

                // Add active class to this button
                button.classList.add('active');

                // Update selected category and repopulate
                selectedSrcCategory = category.id;
                selectedSrcType = 'custom';
                populateSourceOptions();
            });

            // Set ALL as active by default
            if (category.name === 'ALL') {
                button.classList.add('active');
            }

            srcContainer.appendChild(button);
        });
    }

    function renderCategoryLists() {
        // Render destination categories
        destinationCategoryList.innerHTML = '';
        customCategories.destination.forEach(category => {
            const categoryItem = document.createElement('div');
            categoryItem.className = 'category-item';

            const header = document.createElement('div');
            header.className = 'category-header';

            const name = document.createElement('h4');
            name.style.margin = '0';
            name.textContent = category.name;

            const actions = document.createElement('div');
            actions.className = 'category-actions';

            const editButton = document.createElement('button');
            editButton.textContent = 'Edit';
            editButton.addEventListener('click', () => showCategoryForm('destination', category.id));

            const deleteButton = document.createElement('button');
            deleteButton.textContent = 'Delete';
            deleteButton.className = 'delete';
            deleteButton.addEventListener('click', () => deleteCategoryHandler('destination', category.id));

            actions.appendChild(editButton);
            actions.appendChild(deleteButton);

            header.appendChild(name);
            header.appendChild(actions);

            const ioList = document.createElement('div');
            ioList.className = 'io-list';

            category.ios.forEach(io => {
                const ioItem = document.createElement('span');
                ioItem.className = 'io-item';
                ioItem.textContent = destinationNames[io] || `OUT ${io}`;
                ioList.appendChild(ioItem);
            });

            categoryItem.appendChild(header);
            categoryItem.appendChild(ioList);

            destinationCategoryList.appendChild(categoryItem);
        });

        // Render source categories
        sourceCategoryList.innerHTML = '';
        customCategories.source.forEach(category => {
            const categoryItem = document.createElement('div');
            categoryItem.className = 'category-item';

            const header = document.createElement('div');
            header.className = 'category-header';

            const name = document.createElement('h4');
            name.style.margin = '0';
            name.textContent = category.name;

            const actions = document.createElement('div');
            actions.className = 'category-actions';

            const editButton = document.createElement('button');
            editButton.textContent = 'Edit';
            editButton.addEventListener('click', () => showCategoryForm('source', category.id));

            const deleteButton = document.createElement('button');
            deleteButton.textContent = 'Delete';
            deleteButton.className = 'delete';
            deleteButton.addEventListener('click', () => deleteCategoryHandler('source', category.id));

            actions.appendChild(editButton);
            actions.appendChild(deleteButton);

            header.appendChild(name);
            header.appendChild(actions);

            const ioList = document.createElement('div');
            ioList.className = 'io-list';

            category.ios.forEach(io => {
                const ioItem = document.createElement('span');
                ioItem.className = 'io-item';
                ioItem.textContent = sourceNames[io] || `IN ${io}`;
                ioList.appendChild(ioItem);
            });

            categoryItem.appendChild(header);
            categoryItem.appendChild(ioList);

            sourceCategoryList.appendChild(categoryItem);
        });
    }

    // Monitors Tab Functionality
    function initMonitors() {
        // Load saved buttons
        loadMonitorButtons();

        // Set up event listeners
        createMonitorBtn.addEventListener('click', createNewMonitorButton);

        // Set up global copy/paste button listeners
        document.getElementById('copy-button-btn').addEventListener('click', function() {
            copySelectedButtonProperties();
        });

        document.getElementById('paste-button-btn').addEventListener('click', function() {
            pasteToSelectedButton();
        });

        // Button width control
        const buttonWidthSlider = document.getElementById('button-width-slider');
        const buttonWidthValue = document.getElementById('button-width-value');

        // Button height control
        const buttonHeightSlider = document.getElementById('button-height-slider');
        const buttonHeightValue = document.getElementById('button-height-value');

        // Initialize from saved preferences or default
        const savedButtonWidth = localStorage.getItem('monitorButtonWidth') || 100;
        const savedButtonHeight = localStorage.getItem('monitorButtonHeight') || 100;

        buttonWidthSlider.value = savedButtonWidth;
        buttonWidthValue.textContent = `${savedButtonWidth}px`;

        buttonHeightSlider.value = savedButtonHeight;
        buttonHeightValue.textContent = `${savedButtonHeight}px`;

        // Apply default width/height to all buttons if needed
        applyDefaultSizesToAllButtons(savedButtonWidth, savedButtonHeight);

        // Add event listener for width slider changes
        buttonWidthSlider.addEventListener('input', function() {
            const width = this.value;
            buttonWidthValue.textContent = `${width}px`;

            if (selectedMonitorButton) {
                // Only change the width of the selected button
                // Update the width in the button info object
                selectedMonitorButton.width = parseInt(width);

                // Update the DOM element
                const button = document.getElementById(selectedMonitorButton.id);
                if (button) {
                    button.style.width = `${width}px`;
                }

                // Save the changes
                saveMonitorButtons();
            } else {
                // If no button is selected, apply to all buttons
                monitorButtons.forEach(buttonInfo => {
                    buttonInfo.width = parseInt(width);

                    // Update DOM element
                    const button = document.getElementById(buttonInfo.id);
                    if (button) {
                        button.style.width = `${width}px`;
                    }
                });

                // Save the changes
                saveMonitorButtons();
            }

            localStorage.setItem('monitorButtonWidth', width);
        });

        // Add event listener for height slider changes
        buttonHeightSlider.addEventListener('input', function() {
            const height = this.value;
            buttonHeightValue.textContent = `${height}px`;

            if (selectedMonitorButton) {
                // Only change the height of the selected button
                // Update the height in the button info object
                selectedMonitorButton.height = parseInt(height);

                // Update the DOM element
                const button = document.getElementById(selectedMonitorButton.id);
                if (button) {
                    button.style.height = `${height}px`;
                }

                // Save the changes
                saveMonitorButtons();
            } else {
                // If no button is selected, apply to all buttons
                monitorButtons.forEach(buttonInfo => {
                    buttonInfo.height = parseInt(height);

                    // Update DOM element
                    const button = document.getElementById(buttonInfo.id);
                    if (button) {
                        button.style.height = `${height}px`;
                    }
                });

                // Save the changes
                saveMonitorButtons();
            }

            localStorage.setItem('monitorButtonHeight', height);
        });
    }

    // New helper function to ensure all buttons have the correct size
    function applyDefaultSizesToAllButtons(width, height) {
        monitorButtons.forEach(buttonInfo => {
            // Set width and height if not already set
            if (!buttonInfo.width) buttonInfo.width = parseInt(width);
            if (!buttonInfo.height) buttonInfo.height = parseInt(height);

            // Update DOM element
            const button = document.getElementById(buttonInfo.id);
            if (button) {
                button.style.width = `${buttonInfo.width}px`;
                button.style.height = `${buttonInfo.height}px`;
            }
        });
    }

    function createNewMonitorButton() {
        const buttonId = 'monitor-button-' + Date.now();

        // Use separate width and height instead of single size
        const buttonInfo = {
            id: buttonId,
            x: 20,
            y: 20,
            label: 'Monitor ' + (monitorButtons.length + 1),
            assignmentType: null,
            assignment: null,
            width: parseInt(document.getElementById('button-width-slider').value) || 100,
            height: parseInt(document.getElementById('button-height-slider').value) || 100
        };

        monitorButtons.push(buttonInfo);
        renderMonitorButton(buttonInfo);
        selectMonitorButton(buttonInfo);
        saveMonitorButtons();

        showToast('New monitor button created', 'success');
    }

    function renderMonitorButton(buttonInfo) {
        const button = document.createElement('div');
        button.id = buttonInfo.id;
        button.className = 'monitor-button';
        button.style.left = buttonInfo.x + 'px';
        button.style.top = buttonInfo.y + 'px';

        // Set individual button width and height
        button.style.width = `${buttonInfo.width}px`;
        button.style.height = `${buttonInfo.height}px`;

        // Create label element
        const label = document.createElement('div');
        label.className = 'monitor-button-label';
        label.textContent = buttonInfo.label;
        button.appendChild(label);

        // Create assignment element
        const assignment = document.createElement('div');
        assignment.className = 'monitor-button-assignment';
        assignment.textContent = getAssignmentText(buttonInfo);
        button.appendChild(assignment);

        // Create control buttons container
        const controls = document.createElement('div');
        controls.className = 'monitor-button-controls';

        // Add rename button
        const renameBtn = document.createElement('div');
        renameBtn.className = 'monitor-button-rename';
        renameBtn.innerHTML = '✏️';
        renameBtn.title = 'Rename';
        renameBtn.onclick = (e) => {
            e.stopPropagation();
            renameMonitorButton(buttonInfo);
        };
        controls.appendChild(renameBtn);

        // Add remove button
        const removeBtn = document.createElement('div');
        removeBtn.className = 'monitor-button-remove';
        removeBtn.innerHTML = '×';
        removeBtn.title = 'Delete';
        removeBtn.onclick = (e) => {
            e.stopPropagation();
            removeMonitorButton(buttonInfo.id);
        };
        controls.appendChild(removeBtn);

        button.appendChild(controls);

        // Add click event for selecting
        button.addEventListener('click', () => {
            selectMonitorButton(buttonInfo);
        });

        // Make button draggable
        makeDraggable(button);

        // Add to monitor board
        monitorBoard.appendChild(button);
    }

    // Helper function to get the assignment text for a button
    function getAssignmentText(buttonInfo) {
        if (!buttonInfo.assignment) {
            return 'Not assigned';
        }
        if (buttonInfo.assignmentType === 'destination') {
            return destinationNames[buttonInfo.assignment]
                ? `Output ${buttonInfo.assignment}: ${destinationNames[buttonInfo.assignment]}`
                : `Output ${buttonInfo.assignment}`;
        } else if (buttonInfo.assignmentType === 'source') {
            return sourceNames[buttonInfo.assignment]
                ? `Input ${buttonInfo.assignment}: ${sourceNames[buttonInfo.assignment]}`
                : `Input ${buttonInfo.assignment}`;
        }
    }

    function removeMonitorButton(buttonId) {
        // Show a confirmation dialog
        if (confirm('Are you sure you want to delete this button?')) {
            // Find the index of the button in the monitorButtons array
            const index = monitorButtons.findIndex(button => button.id === buttonId);

            if (index !== -1) {
                // Remove from array
                monitorButtons.splice(index, 1);

                // Remove from DOM
                const button = document.getElementById(buttonId);
                if (button) {
                    button.remove();
                }

                // Clear selection if this was the selected button
                if (selectedMonitorButton && selectedMonitorButton.id === buttonId) {
                    selectedMonitorButton = null;
                }

                // Save changes
                saveMonitorButtons();

                showToast('Button deleted', 'success');
            }
        }
    }

    function renameMonitorButton(buttonInfo) {
        // Create custom modal instead of using prompt
        createMonitorButtonModal(buttonInfo);
    }

    function createMonitorButtonModal(buttonInfo) {
        // Remove any existing modal first
        const existingModal = document.querySelector('.monitor-button-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal container
        const modalOverlay = document.createElement('div');
        modalOverlay.className = 'modal-overlay';

        const modal = document.createElement('div');
        modal.className = 'monitor-button-modal';

        // Create modal header
        const modalHeader = document.createElement('div');
        modalHeader.className = 'modal-header';

        const modalTitle = document.createElement('h3');
        modalTitle.textContent = 'Monitor Button Settings';
        modalHeader.appendChild(modalTitle);

        const closeButton = document.createElement('button');
        closeButton.className = 'modal-close';
        closeButton.onclick = () => modalOverlay.remove();
        modalHeader.appendChild(closeButton);

        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';

        // Name field
        const nameGroup = document.createElement('div');
        nameGroup.className = 'form-group';

        const nameLabel = document.createElement('label');
        nameLabel.setAttribute('for', 'button-name');
        nameLabel.textContent = 'Button Name:';
        nameGroup.appendChild(nameLabel);

        const nameInput = document.createElement('input');
        nameInput.type = 'text';
        nameInput.id = 'button-name';
        nameInput.value = buttonInfo.label || '';
        nameGroup.appendChild(nameInput);

        modalContent.appendChild(nameGroup);

        // Assignment section with improved header and description
        const assignmentSectionHeader = document.createElement('div');
        assignmentSectionHeader.className = 'section-header';
        assignmentSectionHeader.innerHTML = '<h4>Router Assignment</h4><p>Assign this button to a router destination (output) to monitor and control it</p>';
        modalContent.appendChild(assignmentSectionHeader);

        // Assignment type select with better labels
        const assignTypeGroup = document.createElement('div');
        assignTypeGroup.className = 'form-group';

        const assignTypeLabel = document.createElement('label');
        assignTypeLabel.setAttribute('for', 'button-assignment-type');
        assignTypeLabel.textContent = 'Assignment Type:';
        assignTypeGroup.appendChild(assignTypeLabel);

        const assignTypeSelect = document.createElement('select');
        assignTypeSelect.id = 'button-assignment-type';

        const noneOption = document.createElement('option');
        noneOption.value = '';
        noneOption.textContent = 'None';
        assignTypeSelect.appendChild(noneOption);

        const destOption = document.createElement('option');
        destOption.value = 'destination';
        destOption.textContent = 'Destination (Output) - Assign button to a router output';
        assignTypeSelect.appendChild(destOption);

        const sourceOption = document.createElement('option');
        sourceOption.value = 'source';
        sourceOption.textContent = 'Source (Input) - Assign button to a router input';
        assignTypeSelect.appendChild(sourceOption);

        // Set selected option based on current assignment
        assignTypeSelect.value = buttonInfo.assignmentType || '';

        assignTypeGroup.appendChild(assignTypeSelect);
        modalContent.appendChild(assignTypeGroup);

        // Assignment value select (initially hidden)
        const assignValueGroup = document.createElement('div');
        assignValueGroup.className = 'form-group';
        assignValueGroup.id = 'assignment-value-group';
        assignValueGroup.style.display = buttonInfo.assignmentType ? 'block' : 'none';

        const assignValueLabel = document.createElement('label');
        assignValueLabel.setAttribute('for', 'button-assignment-value');
        assignValueLabel.textContent = buttonInfo.assignmentType === 'destination' ?
                                     'Assign to Router Destination:' :
                                     'Assign to Router Source:';
        assignValueGroup.appendChild(assignValueLabel);

        const assignValueSelect = document.createElement('select');
        assignValueSelect.id = 'button-assignment-value';
        assignValueGroup.appendChild(assignValueSelect);

        // Populate options based on assignment type
        function populateAssignmentOptions(type) {
            // Clear existing options
            assignValueSelect.innerHTML = '';

            // Update label text based on type
            assignValueLabel.textContent = type === 'destination' ?
                                         'Select Router Destination (Output):' :
                                         'Select Router Source (Input):';

            if (type === 'destination') {
                // Get all destination outputs
                const destinations = Object.keys(destinationNames).sort((a, b) => parseInt(a) - parseInt(b));

                // Add each destination as an option
                destinations.forEach(destNum => {
                    const option = document.createElement('option');
                    option.value = destNum;
                    option.textContent = destinationNames[destNum] ?
                        `Output ${destNum}: ${destinationNames[destNum]}` :
                        `Output ${destNum}`;
                    assignValueSelect.appendChild(option);
                });

                // Add numerically if we don't have destinations from router yet
                if (destinations.length === 0) {
                    for (let i = 1; i <= maxOutputs; i++) {
                        const option = document.createElement('option');
                        option.value = i;
                        option.textContent = `Output ${i}`;
                        assignValueSelect.appendChild(option);
                    }
                }
            } else if (type === 'source') {
                // Get all source inputs
                const sources = Object.keys(sourceNames).sort((a, b) => parseInt(a) - parseInt(b));

                // Add each source as an option
                sources.forEach(srcNum => {
                    const option = document.createElement('option');
                    option.value = srcNum;
                    option.textContent = sourceNames[srcNum] ?
                        `Input ${srcNum}: ${sourceNames[srcNum]}` :
                        `Input ${srcNum}`;
                    assignValueSelect.appendChild(option);
                });

                // Add numerically if we don't have sources from router yet
                if (sources.length === 0) {
                    for (let i = 1; i <= maxInputs; i++) {
                        const option = document.createElement('option');
                        option.value = i;
                        option.textContent = `Input ${i}`;
                        assignValueSelect.appendChild(option);
                    }
                }
            }

            // Set selected value if applicable
            if (buttonInfo.assignment && type === buttonInfo.assignmentType) {
                assignValueSelect.value = buttonInfo.assignment;
            } else if (assignValueSelect.options.length > 0) {
                assignValueSelect.selectedIndex = 0;
            }
        }

        // Initial population of options
        if (buttonInfo.assignmentType) {
            populateAssignmentOptions(buttonInfo.assignmentType);
        }

        // Show/hide and populate assignment value when assignment type changes
        assignTypeSelect.addEventListener('change', function() {
            const type = this.value;

            if (type) {
                populateAssignmentOptions(type);
                assignValueGroup.style.display = 'block';
            } else {
                assignValueGroup.style.display = 'none';
            }
        });

        modalContent.appendChild(assignValueGroup);

        // Add help text explaining the assignment functionality with more detail
        const helpText = document.createElement('div');
        helpText.className = 'help-text';
        helpText.innerHTML = `
            <p><strong>How Router Assignment Works:</strong></p>
            <ul>
                <li><strong>Destination Assignment:</strong> Button will allow you to monitor and control this router output</li>
                <li><strong>Source Assignment:</strong> Button will reference this router input source</li>
            </ul>
        `;
        modalContent.appendChild(helpText);

        // Create modal footer with actions
        const modalFooter = document.createElement('div');
        modalFooter.className = 'modal-footer';

        const cancelButton = document.createElement('button');
        cancelButton.className = 'modal-cancel';
        cancelButton.textContent = 'Cancel';
        cancelButton.onclick = () => modalOverlay.remove();
        modalFooter.appendChild(cancelButton);

        const saveButton = document.createElement('button');
        saveButton.className = 'modal-save';
        saveButton.textContent = 'Save';
        saveButton.onclick = () => {
            // Update button name
            const newName = nameInput.value.trim();
            if (newName) {
                buttonInfo.label = newName;

                // Update the DOM
                const button = document.getElementById(buttonInfo.id);
                if (button) {
                    const labelElement = button.querySelector('.monitor-button-label');
                    if (labelElement) {
                        labelElement.textContent = newName;
                    }
                }
            }

            // Update button assignment
            const newAssignmentType = assignTypeSelect.value;
            let newAssignment = null;

            if (newAssignmentType && assignValueSelect.value) {
                newAssignment = assignValueSelect.value;
            }

            buttonInfo.assignmentType = newAssignmentType;
            buttonInfo.assignment = newAssignment;

            // Update the button's assignment display
            const button = document.getElementById(buttonInfo.id);
            if (button) {
                const assignmentElement = button.querySelector('.monitor-button-assignment');
                if (assignmentElement) {
                    assignmentElement.textContent = getAssignmentText(buttonInfo);
                }
            }

            // Save the changes
            saveMonitorButtons();

            // Close modal
            modalOverlay.remove();

            showToast('Button settings updated', 'success');
        };
        modalFooter.appendChild(saveButton);

        // Assemble modal
        modal.appendChild(modalHeader);
        modal.appendChild(modalContent);
        modal.appendChild(modalFooter);

        modalOverlay.appendChild(modal);
        document.body.appendChild(modalOverlay);

        // Focus name input
        nameInput.focus();
    }

    // Add this CSS to the page for the modal
    function addModalStyles() {
        const styleElement = document.getElementById('modal-styles');
        if (styleElement) return; // Styles already added

        const style = document.createElement('style');
        style.id = 'modal-styles';
        style.textContent = `
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.6);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            }

            .monitor-button-modal {
                background-color: #222;
                border-radius: 4px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
                width: 400px;
                max-width: 90%;
                color: #fff;
                position: relative;
                z-index: 1001;
            }

            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px 20px;
                border-bottom: 1px solid #333;
            }

            .modal-header h3 {
                margin: 0;
                font-size: 18px;
            }

            .modal-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #999;
                position: relative;
                width: 24px;
                height: 24px;
            }

            .modal-close:before,
            .modal-close:after {
                content: '';
                position: absolute;
                top: 50%;
                left: 0;
                width: 100%;
                height: 2px;
                background-color: #999;
            }

            .modal-close:before {
                transform: rotate(45deg);
            }

            .modal-close:after {
                transform: rotate(-45deg);
            }

            .modal-close:hover:before,
            .modal-close:hover:after {
                background-color: #fff;
            }

            .modal-content {
                padding: 20px;
                max-height: 70vh;
                overflow-y: auto;
            }

            .form-group {
                margin-bottom: 15px;
            }

            .form-group label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }

            .form-group input,
            .form-group select {
                width: 100%;
                padding: 8px 10px;
                border-radius: 4px;
                border: 1px solid #444;
                background-color: #333;
                color: #fff;
            }

            .modal-footer {
                padding: 15px 20px;
                text-align: right;
                border-top: 1px solid #333;
            }

            .modal-footer button {
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                margin-left: 10px;
                border: none;
            }

            .modal-cancel {
                background-color: #555;
                color: #fff;
            }

            .modal-save {
                background-color: #4CAF50;
                color: #fff;
            }

            .modal-cancel:hover {
                background-color: #666;
            }

            .modal-save:hover {
                background-color: #3e8e41;
            }

            .section-header {
                margin: 20px 0 10px;
                border-bottom: 1px solid #444;
                padding-bottom: 5px;
            }

            .section-header h4 {
                margin: 0 0 5px;
                font-size: 16px;
            }

            .section-header p {
                margin: 0;
                font-size: 12px;
                color: #999;
            }

            .help-text {
                margin-top: 20px;
                padding: 10px;
                background-color: #333;
                border-radius: 4px;
                font-size: 12px;
            }

            .help-text p {
                margin-top: 0;
            }

            .help-text ul {
                padding-left: 20px;
                margin-bottom: 0;
            }
        `;

        document.head.appendChild(style);

        // Also add toast styles
        const toastStyles = document.createElement('style');
        toastStyles.id = 'toast-styles';
        toastStyles.textContent = `
            .toast-container {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1500;
            }

            .toast {
                padding: 10px 20px;
                margin-top: 10px;
                border-radius: 4px;
                color: white;
                opacity: 0;
                transform: translateY(20px);
                transition: opacity 0.3s, transform 0.3s;
            }

            .toast.show {
                opacity: 1;
                transform: translateY(0);
            }

            .toast.info {
                background-color: #2196F3;
            }

            .toast.success {
                background-color: #4CAF50;
            }

            .toast.error {
                background-color: #F44336;
            }

            .toast.warning {
                background-color: #FF9800;
            }
        `;

        document.head.appendChild(toastStyles);
    }

    function saveMonitorButtons() {
        localStorage.setItem('routerMonitorButtons', JSON.stringify(monitorButtons));
    }

    function loadMonitorButtons() {
        const savedButtons = localStorage.getItem('routerMonitorButtons');
        if (savedButtons) {
            monitorButtons = JSON.parse(savedButtons);
            monitorButtons.forEach(buttonInfo => {
                renderMonitorButton(buttonInfo);
            });
        }
    }

    function enableDraggableButtons() {
        const buttons = monitorBoard.querySelectorAll('.monitor-button');
        buttons.forEach(button => {
            makeDraggable(button);
        });
    }

    function selectMonitorButton(buttonInfo) {
        // Clear selection from all monitor buttons
        document.querySelectorAll('.monitor-button').forEach(btn => {
            btn.classList.remove('selected');
        });

        // Set the new selected button
        selectedMonitorButton = buttonInfo;

        // Add selected class to the button
        const button = document.getElementById(buttonInfo.id);
        if (button) {
            button.classList.add('selected');

            // Update the width and height sliders to reflect this button's dimensions
            const widthSlider = document.getElementById('button-width-slider');
            const widthValue = document.getElementById('button-width-value');
            const heightSlider = document.getElementById('button-height-slider');
            const heightValue = document.getElementById('button-height-value');
            if (widthSlider && widthValue && heightSlider && heightValue) {
                widthSlider.value = buttonInfo.width;
                widthValue.textContent = buttonInfo.width + 'px';
                heightSlider.value = buttonInfo.height;
                heightValue.textContent = buttonInfo.height + 'px';
            }
        }
    }

    function startAssignmentMode() {
        if (!selectedMonitorButton) {
            showToast('Please select a monitor button first', 'error');
            return;
        }

        isAssignmentMode = true;
        document.getElementById('assignment-controls').style.display = 'flex';
        document.getElementById('assignment-type').addEventListener('change', updateAssignmentOptions);

        // Initialize assignment options based on type
        updateAssignmentOptions();

        // Add active class to the monitor board to indicate assignment mode
        monitorBoard.classList.add('assignment-mode');

        showToast('Select a destination or source to assign to the button', 'info');
    }

    function updateAssignmentOptions() {
        const assignmentType = document.getElementById('assignment-type').value;
        const optionsList = document.getElementById('assignment-options');
        optionsList.innerHTML = '';
        if (assignmentType === 'destination') {
            // Use destinationNames for available destinations
            const destKeys = Object.keys(destinationNames).sort((a, b) => parseInt(a) - parseInt(b));
            destKeys.forEach(destNum => {
                const option = document.createElement('option');
                option.value = destNum;
                option.textContent = destinationNames[destNum] ? `Output ${destNum}: ${destinationNames[destNum]}` : `Output ${destNum}`;
                optionsList.appendChild(option);
            });
        } else {
            // Use sourceNames for available sources
            const srcKeys = Object.keys(sourceNames).sort((a, b) => parseInt(a) - parseInt(b));
            srcKeys.forEach(srcNum => {
                const option = document.createElement('option');
                option.value = srcNum;
                option.textContent = sourceNames[srcNum] ? `Input ${srcNum}: ${sourceNames[srcNum]}` : `Input ${srcNum}`;
                optionsList.appendChild(option);
            });
        }
    }

    function assignToButton() {
        if (!selectedMonitorButton || !isAssignmentMode) {
            return;
        }

        const assignmentType = document.getElementById('assignment-type').value;
        const assignmentValue = document.getElementById('assignment-options').value;

        selectedMonitorButton.assignmentType = assignmentType;
        selectedMonitorButton.assignment = assignmentValue;

        updateMonitorButtonAssignment(selectedMonitorButton);

        // Save changes
        saveMonitorButtons();

        // End assignment mode
        cancelAssignmentMode();

        showToast(`Assignment set successfully`, 'success');
    }

    function updateMonitorButtonAssignment(buttonInfo) {
        const button = document.getElementById(buttonInfo.id);
        if (!button) return;

        const assignmentElement = button.querySelector('.monitor-button-assignment');

        if (!buttonInfo.assignment) {
            assignmentElement.textContent = 'Not assigned';
            button.classList.remove('destination-button', 'source-button');
            return;
        }

        if (buttonInfo.assignmentType === 'destination') {
            const destName = destinations[buttonInfo.assignment] || `Destination ${buttonInfo.assignment}`;
            assignmentElement.textContent = destName;
            button.classList.add('destination-button');
            button.classList.remove('source-button');
        } else if (buttonInfo.assignmentType === 'source') {
            const srcName = sources[buttonInfo.assignment] || `Source ${buttonInfo.assignment}`;
            assignmentElement.textContent = srcName;
            button.classList.add('source-button');
            button.classList.remove('destination-button');
        }
    }

    function cancelAssignmentMode() {
        isAssignmentMode = false;
        document.getElementById('assignment-controls').style.display = 'none';
        monitorBoard.classList.remove('assignment-mode');
    }

    function makeDraggable(element) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

        element.onmousedown = dragMouseDown;

        function dragMouseDown(e) {
            if (isAssignmentMode) return; // Don't allow dragging in assignment mode

            e = e || window.event;
            e.preventDefault();

            // Get the mouse cursor position at startup
            pos3 = e.clientX;
            pos4 = e.clientY;

            document.onmouseup = closeDragElement;
            document.onmousemove = elementDrag;

            // Select the button when starting to drag
            const buttonId = element.id;
            const buttonInfo = monitorButtons.find(btn => btn.id === buttonId);
            if (buttonInfo) {
                selectMonitorButton(buttonInfo);
            }
        }

        function elementDrag(e) {
            e = e || window.event;
            e.preventDefault();

            // Calculate the new cursor position
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;

            // Ensure button stays within the board boundaries
            const board = monitorBoard.getBoundingClientRect();
            const elem = element.getBoundingClientRect();

            let newTop = element.offsetTop - pos2;
            let newLeft = element.offsetLeft - pos1;

            // Apply bounds checking
            if (newTop < 0) newTop = 0;
            if (newLeft < 0) newLeft = 0;
            if (newTop > board.height - elem.height) newTop = board.height - elem.height;
            if (newLeft > board.width - elem.width) newLeft = board.width - elem.width;

            // Set the element's new position
            element.style.top = newTop + "px";
            element.style.left = newLeft + "px";

            // Update position in our data structure
            const buttonId = element.id;
            const buttonInfo = monitorButtons.find(btn => btn.id === buttonId);
            if (buttonInfo) {
                buttonInfo.x = newLeft;
                buttonInfo.y = newTop;
            }
        }

        function closeDragElement() {
            // Stop moving when mouse button is released
            document.onmouseup = null;
            document.onmousemove = null;

            // Save the button positions
            saveMonitorButtons();
        }
    }

    function updateMonitorButtonLabels() {
        monitorButtons.forEach(buttonInfo => {
            updateMonitorButtonAssignment(buttonInfo);
        });
    }

    // Copy button properties
    function copyButtonProperties(buttonInfo) {
        copiedButtonProperties = {
            label: buttonInfo.label,
            assignmentType: buttonInfo.assignmentType,
            assignment: buttonInfo.assignment,
            width: buttonInfo.width,
            height: buttonInfo.height
        };
        showToast('Button properties copied', 'success');
    }

    // Paste button properties
    function pasteButtonProperties(targetButtonInfo) {
        if (!copiedButtonProperties) {
            showToast('No properties to paste', 'error');
            return;
        }

        // Apply copied properties to target button
        targetButtonInfo.label = copiedButtonProperties.label;
        targetButtonInfo.assignmentType = copiedButtonProperties.assignmentType;
        targetButtonInfo.assignment = copiedButtonProperties.assignment;
        targetButtonInfo.width = copiedButtonProperties.width;
        targetButtonInfo.height = copiedButtonProperties.height;

        // Update the DOM
        const button = document.getElementById(targetButtonInfo.id);
        if (button) {
            // Update label
            const labelElement = button.querySelector('.monitor-button-label');
            if (labelElement) {
                labelElement.textContent = targetButtonInfo.label;
            }

            // Update assignment
            const assignmentElement = button.querySelector('.monitor-button-assignment');
            if (assignmentElement) {
                assignmentElement.textContent = getAssignmentText(targetButtonInfo);
            }

            // Update width and height
            button.style.width = `${targetButtonInfo.width}px`;
            button.style.height = `${targetButtonInfo.height}px`;
        }

        // Save changes
        saveMonitorButtons();

        showToast('Button properties pasted', 'success');
    }

    // Global copy/paste functions to be called from external buttons
    function copySelectedButtonProperties() {
        if (!selectedMonitorButton) {
            showToast('Please select a monitor button first', 'error');
            return;
        }

        copiedButtonProperties = {
            label: selectedMonitorButton.label,
            assignmentType: selectedMonitorButton.assignmentType,
            assignment: selectedMonitorButton.assignment,
            width: selectedMonitorButton.width,
            height: selectedMonitorButton.height
        };

        showToast('Button properties copied', 'success');
    }

    function pasteToSelectedButton() {
        if (!selectedMonitorButton) {
            showToast('Please select a monitor button first', 'error');
            return;
        }

        if (!copiedButtonProperties) {
            showToast('No properties to paste', 'error');
            return;
        }

        // Apply copied properties to selected button
        selectedMonitorButton.label = copiedButtonProperties.label;
        selectedMonitorButton.assignmentType = copiedButtonProperties.assignmentType;
        selectedMonitorButton.assignment = copiedButtonProperties.assignment;
        selectedMonitorButton.width = copiedButtonProperties.width;
        selectedMonitorButton.height = copiedButtonProperties.height;

        // Update the DOM
        const button = document.getElementById(selectedMonitorButton.id);
        if (button) {
            // Update label
            const labelElement = button.querySelector('.monitor-button-label');
            if (labelElement) {
                labelElement.textContent = selectedMonitorButton.label;
            }

            // Update assignment
            const assignmentElement = button.querySelector('.monitor-button-assignment');
            if (assignmentElement) {
                assignmentElement.textContent = getAssignmentText(selectedMonitorButton);
            }

            // Update width and height
            button.style.width = `${selectedMonitorButton.width}px`;
            button.style.height = `${selectedMonitorButton.height}px`;
        }

        // Save changes
        saveMonitorButtons();

        showToast('Button properties pasted', 'success');
    }

    function performTake() {
        if (selectedInput && selectedOutput) {
            // Get the take command for the current protocol
            const takeCmd = getProtocolCommand('take', selectedOutput, selectedInput);
            if (takeCmd) {
                sendRouterCommand(takeCmd);
                addLogEntry(`Taking input ${selectedInput} to output ${selectedOutput}`, 'command');
            }
        }
    }

    function updateTakeButton() {
        takeBtn.disabled = !selectedInput || !wsConnection || wsConnection.readyState !== WebSocket.OPEN;
    }

    function toggleLock() {
        if (isLocked) {
            // Get the unlock command for the current protocol
            const unlockCmd = getProtocolCommand('unlock', selectedOutput);
            if (unlockCmd) {
                sendRouterCommand(unlockCmd);
            }
        } else {
            // Get the lock command for the current protocol
            const lockCmd = getProtocolCommand('lock', selectedOutput);
            if (lockCmd) {
                sendRouterCommand(lockCmd);
            }
        }
    }

    // Setup Tab Functions
    function loadRouterConfig() {
        const savedConfig = localStorage.getItem('routerConfig');
        if (savedConfig) {
            try {
                routerConfig = JSON.parse(savedConfig);
                routerIpInput.value = routerConfig.host;
                routerPortInput.value = routerConfig.port;

                // Load protocol if available
                if (routerConfig.protocol) {
                    routerProtocolSelect.value = routerConfig.protocol;
                    protocolConfig.type = routerConfig.protocol;
                    updateProtocolInfo();
                }

                // Load router size if available
                if (routerConfig.inputs) {
                    document.getElementById('router-inputs').value = routerConfig.inputs;
                    maxInputs = routerConfig.inputs;
                }
                if (routerConfig.outputs) {
                    document.getElementById('router-outputs').value = routerConfig.outputs;
                    maxOutputs = routerConfig.outputs;
                }

                // Update the router address display in the Router Control tab
                const routerAddressDisplay = document.querySelector('.status-bar > div > span');
                if (routerAddressDisplay) {
                    routerAddressDisplay.textContent = `Router: ${routerConfig.host}:${routerConfig.port}`;
                }

                // Update the ALL categories with the new router size
                updateAllCategoriesWithRouterSize();

                // Update Names tab if it's using the new layout
                if (document.getElementById('inputs-list')) {
                    populateNamesList();
                }

                showToast('Router configuration loaded', 'success');
            } catch (error) {
                showToast('Error loading router configuration', 'error');
                console.error('Error loading router configuration:', error);
            }
        }
    }

    function saveRouterConfig() {
        routerConfig.host = routerIpInput.value;
        routerConfig.port = parseInt(routerPortInput.value);
        routerConfig.protocol = routerProtocolSelect.value;

        // Save router size
        routerConfig.inputs = parseInt(document.getElementById('router-inputs').value) || 96;
        routerConfig.outputs = parseInt(document.getElementById('router-outputs').value) || 384;

        // Update maxInputs and maxOutputs
        maxInputs = routerConfig.inputs;
        maxOutputs = routerConfig.outputs;

        // Update protocol config
        protocolConfig.type = routerProtocolSelect.value;
        localStorage.setItem('protocolConfig', JSON.stringify(protocolConfig));

        // Update the protocol info display
        updateProtocolInfo();

        // Update the ALL categories with the new router size
        updateAllCategoriesWithRouterSize();

        localStorage.setItem('routerConfig', JSON.stringify(routerConfig));

        // Update the router address display in the Router Control tab
        const routerAddressDisplay = document.querySelector('.status-bar > div > span');
        if (routerAddressDisplay) {
            routerAddressDisplay.textContent = `Router: ${routerConfig.host}:${routerConfig.port}`;
        }

        showToast('Router configuration saved', 'success');

        // Update Names tab if it's using the new layout
        if (document.getElementById('inputs-list')) {
            populateNamesList();
        }

        // If we're connected, send the complete configuration to the server
        if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
            // Send complete router configuration including protocol
            const completeConfig = {
                host: routerConfig.host,
                port: routerConfig.port,
                protocol: routerConfig.protocol,
                inputs: routerConfig.inputs,
                outputs: routerConfig.outputs
            };

            wsConnection.send(JSON.stringify({
                command: 'updateRouterConfig',
                config: completeConfig
            }));

            addLogEntry(`Router configuration updated: ${routerConfig.host}:${routerConfig.port}, Protocol: ${routerConfig.protocol}, Inputs: ${routerConfig.inputs}, Outputs: ${routerConfig.outputs}`, 'command');
        }
    }

    // Update the ALL categories with the current router size
    function updateAllCategoriesWithRouterSize() {
        // Update the ALL categories arrays
        const allDestinationRange = Array.from({length: maxOutputs}, (_, i) => i + 1);
        const allSourceRange = Array.from({length: maxInputs}, (_, i) => i + 1);

        // Update the ALL categories in customCategories
        const allDestCategory = customCategories.destination.find(cat => cat.name === 'ALL');
        if (allDestCategory) {
            allDestCategory.ios = allDestinationRange;
        }

        const allSrcCategory = customCategories.source.find(cat => cat.name === 'ALL');
        if (allSrcCategory) {
            allSrcCategory.ios = allSourceRange;
        }

        // Save the updated categories
        saveCustomCategories();

        // Refresh the UI
        populateDestinationOptions();
        populateSourceOptions();
    }

    function loadAppSettings() {
        const savedSettings = localStorage.getItem('appSettings');
        if (savedSettings) {
            try {
                appSettings = JSON.parse(savedSettings);
                autoConnectCheckbox.checked = appSettings.autoConnect;
                defaultTabSelect.value = appSettings.defaultTab;

                showToast('App settings loaded', 'success');
            } catch (error) {
                showToast('Error loading app settings', 'error');
                console.error('Error loading app settings:', error);
            }
        }
    }

    function saveAppSettings() {
        appSettings.autoConnect = autoConnectCheckbox.checked;
        appSettings.defaultTab = defaultTabSelect.value;

        localStorage.setItem('appSettings', JSON.stringify(appSettings));

        showToast('App settings saved', 'success');
    }

    function loadProtocolConfig() {
        const savedProtocol = localStorage.getItem('protocolConfig');
        if (savedProtocol) {
            try {
                protocolConfig = JSON.parse(savedProtocol);
                routerProtocolSelect.value = protocolConfig.type;
                updateProtocolInfo();

                showToast('Protocol configuration loaded', 'success');
            } catch (error) {
                showToast('Error loading protocol configuration', 'error');
                console.error('Error loading protocol configuration:', error);
            }
        }
    }

    // saveProtocolConfig function is now integrated into saveRouterConfig

    function updateProtocolInfo() {
        const protocol = protocolConfig.type;

        // Update the protocol info section
        if (protocol === 'quartz') {
            protocolInfo.innerHTML = `
                <h4>Quartz Protocol</h4>
                <p>Protocol for Evertz Quartz routers.</p>
                <div class="protocol-commands">
                    <div class="command-item">
                        <span class="command-name">Take:</span>
                        <code>.SV{destination},{source}</code>
                    </div>
                    <div class="command-item">
                        <span class="command-name">Lock:</span>
                        <code>.BL{destination}</code>
                    </div>
                    <div class="command-item">
                        <span class="command-name">Unlock:</span>
                        <code>.BU{destination}</code>
                    </div>
                    <div class="command-item">
                        <span class="command-name">Query Status:</span>
                        <code>.IV{destination}</code>
                    </div>
                </div>
            `;
        } else if (protocol === 'rcp3') {
            protocolInfo.innerHTML = `
                <h4>RCP3 (Utah) Protocol</h4>
                <p>Protocol for Utah Scientific routers using the RCP3 packet-based protocol.</p>
                <div class="protocol-commands">
                    <div class="command-item">
                        <span class="command-name">Take:</span>
                        <code>Packet-based Take command</code>
                    </div>
                    <div class="command-item">
                        <span class="command-name">Lock:</span>
                        <code>Set Lock command (1B lock)</code>
                    </div>
                    <div class="command-item">
                        <span class="command-name">Unlock:</span>
                        <code>Clear Lock command</code>
                    </div>
                    <div class="command-item">
                        <span class="command-name">Query Status:</span>
                        <code>Get Matrix command</code>
                    </div>
                </div>
                <p><small>Note: RCP3 uses a binary packet format with headers and checksums.</small></p>
            `;
        } else {
            // For future protocols
            protocolInfo.innerHTML = `<h4>${protocol} Protocol</h4><p>Protocol details will be displayed here.</p>`;
        }
    }

    // Crosspoint Presets
    let crosspointPresets = [];
    let currentPresetProgress = { total: 0, completed: 0 };

    // Preset Elements
    const presetNameInput = document.getElementById('preset-name');
    const savePresetBtn = document.getElementById('save-preset-btn');
    const deletePresetBtn = document.getElementById('delete-preset-btn');
    const presetSelect = document.getElementById('preset-select');
    const recallPresetBtn = document.getElementById('recall-preset-btn');
    const exportPresetsBtn = document.getElementById('export-presets-btn');
    const importPresetsBtn = document.getElementById('import-presets-btn');
    const importPresetsFile = document.getElementById('import-presets-file');
    const presetProgress = document.getElementById('preset-progress');
    const presetProgressBar = document.getElementById('preset-progress-bar');
    const presetProgressText = document.getElementById('preset-progress-text');

    function initSetupTab() {
        // Load saved configuration and settings
        loadRouterConfig();
        loadAppSettings();
        loadCrosspointPresets();

        // Add event listeners for setup tab buttons
        saveRouterConfigBtn.addEventListener('click', saveRouterConfig);
        saveAppSettingsBtn.addEventListener('click', saveAppSettings);

        // Add event listener for protocol selection change
        routerProtocolSelect.addEventListener('change', updateProtocolInfo);

        // Add event listeners for router size changes to update Names tab in real-time
        const routerInputsField = document.getElementById('router-inputs');
        const routerOutputsField = document.getElementById('router-outputs');

        if (routerInputsField) {
            routerInputsField.addEventListener('input', () => {
                // Update routerConfig temporarily for Names tab preview
                const newInputs = parseInt(routerInputsField.value) || 96;
                if (newInputs !== routerConfig.inputs) {
                    routerConfig.inputs = newInputs;
                    maxInputs = newInputs;
                    // Update Names tab if it's visible and using new layout
                    if (document.getElementById('inputs-list')) {
                        populateNamesList();
                    }
                }
            });
        }

        if (routerOutputsField) {
            routerOutputsField.addEventListener('input', () => {
                // Update routerConfig temporarily for Names tab preview
                const newOutputs = parseInt(routerOutputsField.value) || 384;
                if (newOutputs !== routerConfig.outputs) {
                    routerConfig.outputs = newOutputs;
                    maxOutputs = newOutputs;
                    // Update Names tab if it's visible and using new layout
                    if (document.getElementById('inputs-list')) {
                        populateNamesList();
                    }
                }
            });
        }

        // Add event listeners for crosspoint preset buttons
        savePresetBtn.addEventListener('click', saveCurrentCrosspointState);
        deletePresetBtn.addEventListener('click', deleteSelectedPreset);
        presetSelect.addEventListener('change', updatePresetButtons);
        recallPresetBtn.addEventListener('click', recallSelectedPreset);
        exportPresetsBtn.addEventListener('click', exportCrosspointPresets);
        importPresetsBtn.addEventListener('click', () => importPresetsFile.click());
        importPresetsFile.addEventListener('change', (e) => {
            if (e.target.files && e.target.files[0]) {
                importCrosspointPresetsFromFile(e.target.files[0]);
            }
        });

        // Initialize preset buttons state
        updatePresetButtons();
    }

    // Initialize Names Tab
    function initNamesTab() {
        // Get elements
        const nameType = document.getElementById('name-type');
        const nameNumber = document.getElementById('name-number');
        const routerNameInput = document.getElementById('router-name');
        const switcherNameInput = document.getElementById('switcher-name');
        const multiviewerNameInput = document.getElementById('multiviewer-name');
        const addNameBtn = document.getElementById('add-name-btn');
        const deleteNameBtn = document.getElementById('delete-name-btn');
        const exportNamesBtn = document.getElementById('export-names-btn');
        const importNamesBtn = document.getElementById('import-names-btn');
        const importNamesFile = document.getElementById('import-names-file');

        // New elements for column layout
        const inputsSearch = document.getElementById('inputs-search');
        const outputsSearch = document.getElementById('outputs-search');

        // Load saved names
        loadRouterNames();

        // Add event listeners
        addNameBtn.addEventListener('click', addOrUpdateName);
        deleteNameBtn.addEventListener('click', deleteSelectedName);

        // Add Clear button event listener
        const clearNameBtn = document.getElementById('clear-name-btn');
        if (clearNameBtn) {
            clearNameBtn.addEventListener('click', clearNameForm);
        }

        exportNamesBtn.addEventListener('click', exportRouterNames);
        importNamesBtn.addEventListener('click', () => importNamesFile.click());
        importNamesFile.addEventListener('change', (e) => {
            if (e.target.files && e.target.files[0]) {
                importRouterNamesFromFile(e.target.files[0]);
            }
        });

        // Add Enter key support for name input fields
        const nameInputFields = [
            document.getElementById('router-name'),
            document.getElementById('switcher-name'),
            document.getElementById('multiviewer-name')
        ];

        nameInputFields.forEach((field, index) => {
            if (field) {
                field.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();

                        // If this is the last field or user is holding Shift, save the name
                        if (e.shiftKey || index === nameInputFields.length - 1) {
                            addOrUpdateName();
                        } else {
                            // Move to next field
                            const nextField = nameInputFields[index + 1];
                            if (nextField) {
                                nextField.focus();
                            } else {
                                addOrUpdateName();
                            }
                        }
                    }
                });

                // Add Tab key support for quick navigation and Escape for clearing
                field.addEventListener('keydown', (e) => {
                    if (e.key === 'Tab' && e.shiftKey && index > 0) {
                        // Shift+Tab: go to previous field
                        e.preventDefault();
                        nameInputFields[index - 1].focus();
                    } else if (e.key === 'Tab' && !e.shiftKey && index < nameInputFields.length - 1) {
                        // Tab: go to next field
                        e.preventDefault();
                        nameInputFields[index + 1].focus();
                    } else if (e.key === 'Escape') {
                        // Escape: clear all fields and reset selection
                        e.preventDefault();
                        clearNameForm();
                    }
                });
            }
        });

        // Add Enter key support for number field
        const nameNumberField = document.getElementById('name-number');
        if (nameNumberField) {
            nameNumberField.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    // Focus on router name field for quick entry
                    const routerNameField = document.getElementById('router-name');
                    if (routerNameField) {
                        routerNameField.focus();
                    }
                }
            });
        }

        // Add search event listeners for new layout
        if (inputsSearch) {
            inputsSearch.addEventListener('input', () => populateNamesList());
        }
        if (outputsSearch) {
            outputsSearch.addEventListener('input', () => populateNamesList());
        }

        // Initialize names lists (new layout) or table (legacy)
        if (document.getElementById('inputs-list')) {
            populateNamesList();
        } else {
            // Legacy table support
            const namesFilter = document.getElementById('names-filter');
            const namesSearch = document.getElementById('names-search');
            if (namesFilter) namesFilter.addEventListener('change', filterNames);
            if (namesSearch) namesSearch.addEventListener('input', filterNames);
            populateNamesTable();
        }

        // Initialize delete button state
        updateDeleteNameButton();
    }

    // Load router names from localStorage
    function loadRouterNames() {
        const savedNames = localStorage.getItem('routerNames');
        if (savedNames) {
            try {
                routerNames = JSON.parse(savedNames);
                showToast('Router names loaded', 'success');
            } catch (error) {
                showToast('Error loading router names', 'error');
                console.error('Error loading router names:', error);
            }
        }
    }

    // Save router names to localStorage
    function saveRouterNames() {
        localStorage.setItem('routerNames', JSON.stringify(routerNames));
    }

    // Add or update a name
    function addOrUpdateName() {
        const nameType = document.getElementById('name-type').value;
        const nameNumber = parseInt(document.getElementById('name-number').value);
        const routerName = document.getElementById('router-name').value.trim();
        const switcherName = document.getElementById('switcher-name').value.trim();
        const multiviewerName = document.getElementById('multiviewer-name').value.trim();

        if (!nameNumber || nameNumber < 1) {
            showToast('Please enter a valid number', 'error');
            return;
        }

        // Create or update the name entry
        if (nameType === 'in') {
            routerNames.inputs[nameNumber] = {
                routerName,
                switcherName,
                multiviewerName
            };

            // If router name is provided, send command to update router
            if (routerName) {
                sendNameUpdateToRouter('in', nameNumber, routerName);
            }
        } else {
            routerNames.outputs[nameNumber] = {
                routerName,
                switcherName,
                multiviewerName
            };

            // If router name is provided, send command to update router
            if (routerName) {
                sendNameUpdateToRouter('out', nameNumber, routerName);
            }
        }

        // Save to localStorage
        saveRouterNames();

        // Update the display (new layout or legacy table)
        if (document.getElementById('inputs-list')) {
            populateNamesList();
        } else {
            populateNamesTable();
        }

        // Clear form fields
        document.getElementById('router-name').value = '';
        document.getElementById('switcher-name').value = '';
        document.getElementById('multiviewer-name').value = '';

        // Auto-increment number for quick sequential entry
        const nameNumberField = document.getElementById('name-number');
        if (nameNumberField) {
            const currentNumber = parseInt(nameNumberField.value);
            const maxNumber = nameType === 'in' ? routerConfig.inputs : routerConfig.outputs;

            if (currentNumber < maxNumber) {
                nameNumberField.value = currentNumber + 1;
            }
        }

        // Focus back on router name field for quick entry
        const routerNameField = document.getElementById('router-name');
        if (routerNameField) {
            routerNameField.focus();
        }

        showToast(`${nameType.toUpperCase()} ${nameNumber} name updated`, 'success');
    }

    // Send name update command to router
    function sendNameUpdateToRouter(type, number, name) {
        if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
            let command;

            if (type === 'in') {
                command = getProtocolCommand('setSourceName', number, name);
            } else {
                command = getProtocolCommand('setDestinationName', number, name);
            }

            wsConnection.send(JSON.stringify({
                command: command
            }));

            addLogEntry(`Sent name update: ${type.toUpperCase()} ${number} = "${name}"`, 'command');
        } else {
            addLogEntry('Cannot send name update, WebSocket not connected', 'error');
        }
    }

    // Delete selected name
    function deleteSelectedName() {
        if (!selectedNameRow) {
            showToast('Please select a name to delete', 'error');
            return;
        }

        const type = selectedNameRow.dataset.type;
        const number = parseInt(selectedNameRow.dataset.number);

        if (type === 'in') {
            delete routerNames.inputs[number];
        } else {
            delete routerNames.outputs[number];
        }

        // Save to localStorage
        saveRouterNames();

        // Update the display (new layout or legacy table)
        if (document.getElementById('inputs-list')) {
            populateNamesList();
        } else {
            populateNamesTable();
        }

        // Reset selection
        selectedNameRow = null;
        updateDeleteNameButton();

        showToast(`${type.toUpperCase()} ${number} name deleted`, 'success');
    }

    // Populate names lists (new column layout)
    function populateNamesList() {
        const inputsList = document.getElementById('inputs-list');
        const outputsList = document.getElementById('outputs-list');
        const inputsCount = document.getElementById('inputs-count');
        const outputsCount = document.getElementById('outputs-count');
        const inputsSearch = document.getElementById('inputs-search');
        const outputsSearch = document.getElementById('outputs-search');

        if (!inputsList || !outputsList) {
            // Fallback to legacy table if new elements don't exist
            populateNamesTable();
            return;
        }

        // Get router configuration for max inputs/outputs
        const maxInputs = routerConfig.inputs || 96;
        const maxOutputs = routerConfig.outputs || 384;

        // Get search terms
        const inputSearchText = inputsSearch ? inputsSearch.value.toLowerCase() : '';
        const outputSearchText = outputsSearch ? outputsSearch.value.toLowerCase() : '';

        // Clear existing lists
        inputsList.innerHTML = '';
        outputsList.innerHTML = '';

        let inputsDisplayed = 0;
        let outputsDisplayed = 0;

        // Populate inputs list
        for (let i = 1; i <= maxInputs; i++) {
            const names = routerNames.inputs[i] || {};
            const routerName = sourceNames[i] || names.routerName || '';

            // Apply search filter
            if (inputSearchText &&
                !(i.toString().includes(inputSearchText) ||
                  (routerName && routerName.toLowerCase().includes(inputSearchText)) ||
                  (names.switcherName && names.switcherName.toLowerCase().includes(inputSearchText)) ||
                  (names.multiviewerName && names.multiviewerName.toLowerCase().includes(inputSearchText)))) {
                continue;
            }

            const item = document.createElement('div');
            item.className = `names-item ${!routerName ? 'empty' : ''}`;
            item.dataset.type = 'in';
            item.dataset.number = i;

            item.innerHTML = `
                <div class="names-item-number">Input ${i}</div>
                <div class="names-item-name">
                    <div class="names-item-router-name">${routerName || 'No name'}</div>
                    ${names.switcherName || names.multiviewerName ?
                        `<div class="names-item-other-names">
                            ${names.switcherName ? `SW: ${names.switcherName}` : ''}
                            ${names.multiviewerName ? `MV: ${names.multiviewerName}` : ''}
                        </div>` : ''}
                </div>
            `;

            // Add click event to select item
            item.addEventListener('click', () => {
                // Deselect previously selected items
                document.querySelectorAll('.names-item.selected').forEach(el => {
                    el.classList.remove('selected');
                });

                // Select this item
                item.classList.add('selected');
                selectedNameRow = item;

                // Fill form fields with selected name data
                document.getElementById('name-type').value = 'in';
                document.getElementById('name-number').value = i;
                document.getElementById('router-name').value = routerName || '';
                document.getElementById('switcher-name').value = names.switcherName || '';
                document.getElementById('multiviewer-name').value = names.multiviewerName || '';

                // Update delete button state
                updateDeleteNameButton();
            });

            inputsList.appendChild(item);
            inputsDisplayed++;
        }

        // Populate outputs list
        for (let i = 1; i <= maxOutputs; i++) {
            const names = routerNames.outputs[i] || {};
            const routerName = destinationNames[i] || names.routerName || '';

            // Apply search filter
            if (outputSearchText &&
                !(i.toString().includes(outputSearchText) ||
                  (routerName && routerName.toLowerCase().includes(outputSearchText)) ||
                  (names.switcherName && names.switcherName.toLowerCase().includes(outputSearchText)) ||
                  (names.multiviewerName && names.multiviewerName.toLowerCase().includes(outputSearchText)))) {
                continue;
            }

            const item = document.createElement('div');
            item.className = `names-item ${!routerName ? 'empty' : ''}`;
            item.dataset.type = 'out';
            item.dataset.number = i;

            item.innerHTML = `
                <div class="names-item-number">Output ${i}</div>
                <div class="names-item-name">
                    <div class="names-item-router-name">${routerName || 'No name'}</div>
                    ${names.switcherName || names.multiviewerName ?
                        `<div class="names-item-other-names">
                            ${names.switcherName ? `SW: ${names.switcherName}` : ''}
                            ${names.multiviewerName ? `MV: ${names.multiviewerName}` : ''}
                        </div>` : ''}
                </div>
            `;

            // Add click event to select item
            item.addEventListener('click', () => {
                // Deselect previously selected items
                document.querySelectorAll('.names-item.selected').forEach(el => {
                    el.classList.remove('selected');
                });

                // Select this item
                item.classList.add('selected');
                selectedNameRow = item;

                // Fill form fields with selected name data
                document.getElementById('name-type').value = 'out';
                document.getElementById('name-number').value = i;
                document.getElementById('router-name').value = routerName || '';
                document.getElementById('switcher-name').value = names.switcherName || '';
                document.getElementById('multiviewer-name').value = names.multiviewerName || '';

                // Update delete button state
                updateDeleteNameButton();
            });

            outputsList.appendChild(item);
            outputsDisplayed++;
        }

        // Update counts
        if (inputsCount) {
            inputsCount.textContent = `${inputsDisplayed} inputs`;
        }
        if (outputsCount) {
            outputsCount.textContent = `${outputsDisplayed} outputs`;
        }
    }

    // Populate names table (legacy support)
    function populateNamesTable() {
        const namesTableBody = document.getElementById('names-table-body');
        if (!namesTableBody) return;

        const namesFilter = document.getElementById('names-filter');
        const namesSearch = document.getElementById('names-search');
        const filterValue = namesFilter ? namesFilter.value : 'all';
        const searchText = namesSearch ? namesSearch.value.toLowerCase() : '';

        // Clear existing rows
        namesTableBody.innerHTML = '';

        // Add rows for inputs
        if (filterValue === 'all' || filterValue === 'in') {
            Object.entries(routerNames.inputs).forEach(([number, names]) => {
                // Skip if doesn't match search
                if (searchText &&
                    !(number.toLowerCase().includes(searchText) ||
                      (names.routerName && names.routerName.toLowerCase().includes(searchText)) ||
                      (names.switcherName && names.switcherName.toLowerCase().includes(searchText)) ||
                      (names.multiviewerName && names.multiviewerName.toLowerCase().includes(searchText)))) {
                    return;
                }

                const row = document.createElement('tr');
                row.dataset.type = 'in';
                row.dataset.number = number;

                row.innerHTML = `
                    <td>IN</td>
                    <td>${number}</td>
                    <td>${names.routerName || ''}</td>
                    <td>${names.switcherName || ''}</td>
                    <td>${names.multiviewerName || ''}</td>
                `;

                // Add click event to select row
                row.addEventListener('click', () => {
                    // Deselect previously selected row
                    if (selectedNameRow) {
                        selectedNameRow.classList.remove('selected');
                    }

                    // Select this row
                    row.classList.add('selected');
                    selectedNameRow = row;

                    // Fill form fields with selected name data
                    document.getElementById('name-type').value = 'in';
                    document.getElementById('name-number').value = number;
                    document.getElementById('router-name').value = names.routerName || '';
                    document.getElementById('switcher-name').value = names.switcherName || '';
                    document.getElementById('multiviewer-name').value = names.multiviewerName || '';

                    // Update delete button state
                    updateDeleteNameButton();
                });

                namesTableBody.appendChild(row);
            });
        }

        // Add rows for outputs
        if (filterValue === 'all' || filterValue === 'out') {
            Object.entries(routerNames.outputs).forEach(([number, names]) => {
                // Skip if doesn't match search
                if (searchText &&
                    !(number.toLowerCase().includes(searchText) ||
                      (names.routerName && names.routerName.toLowerCase().includes(searchText)) ||
                      (names.switcherName && names.switcherName.toLowerCase().includes(searchText)) ||
                      (names.multiviewerName && names.multiviewerName.toLowerCase().includes(searchText)))) {
                    return;
                }

                const row = document.createElement('tr');
                row.dataset.type = 'out';
                row.dataset.number = number;

                row.innerHTML = `
                    <td>OUT</td>
                    <td>${number}</td>
                    <td>${names.routerName || ''}</td>
                    <td>${names.switcherName || ''}</td>
                    <td>${names.multiviewerName || ''}</td>
                `;

                // Add click event to select row
                row.addEventListener('click', () => {
                    // Deselect previously selected row
                    if (selectedNameRow) {
                        selectedNameRow.classList.remove('selected');
                    }

                    // Select this row
                    row.classList.add('selected');
                    selectedNameRow = row;

                    // Fill form fields with selected name data
                    document.getElementById('name-type').value = 'out';
                    document.getElementById('name-number').value = number;
                    document.getElementById('router-name').value = names.routerName || '';
                    document.getElementById('switcher-name').value = names.switcherName || '';
                    document.getElementById('multiviewer-name').value = names.multiviewerName || '';

                    // Update delete button state
                    updateDeleteNameButton();
                });

                namesTableBody.appendChild(row);
            });
        }
    }

    // Filter names based on filter and search
    function filterNames() {
        populateNamesTable();
    }

    // Update delete name button state
    function updateDeleteNameButton() {
        const deleteNameBtn = document.getElementById('delete-name-btn');
        deleteNameBtn.disabled = !selectedNameRow;
    }

    // Clear name form and reset selection
    function clearNameForm() {
        // Clear all input fields
        document.getElementById('router-name').value = '';
        document.getElementById('switcher-name').value = '';
        document.getElementById('multiviewer-name').value = '';

        // Reset selection
        if (selectedNameRow) {
            selectedNameRow.classList.remove('selected');
            selectedNameRow = null;
        }

        // Clear any selected items in the new layout
        document.querySelectorAll('.names-item.selected').forEach(el => {
            el.classList.remove('selected');
        });

        // Update delete button state
        updateDeleteNameButton();

        // Focus on number field for new entry
        const nameNumberField = document.getElementById('name-number');
        if (nameNumberField) {
            nameNumberField.focus();
        }
    }

    // Export router names to JSON file
    function exportRouterNames() {
        const dataStr = JSON.stringify(routerNames, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

        const exportFileDefaultName = 'router-names.json';

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();

        showToast('Router names exported', 'success');
    }

    // Import router names from JSON file
    function importRouterNamesFromFile(file) {
        const reader = new FileReader();

        reader.onload = (e) => {
            try {
                const importedNames = JSON.parse(e.target.result);

                // Validate structure
                if (importedNames.inputs && importedNames.outputs) {
                    routerNames = importedNames;
                    saveRouterNames();
                    populateNamesTable();
                    showToast('Router names imported successfully', 'success');
                } else {
                    showToast('Invalid router names file format', 'error');
                }
            } catch (error) {
                showToast('Error importing router names', 'error');
                console.error('Error importing router names:', error);
            }
        };

        reader.readAsText(file);
    }

    // Load saved crosspoint presets from localStorage
    function loadCrosspointPresets() {
        const savedPresets = localStorage.getItem('crosspointPresets');
        if (savedPresets) {
            crosspointPresets = JSON.parse(savedPresets);
            populatePresetSelect();
        }
    }

    // Save crosspoint presets to localStorage
    function saveCrosspointPresets() {
        localStorage.setItem('crosspointPresets', JSON.stringify(crosspointPresets));
    }

    // Populate the preset select dropdown
    function populatePresetSelect() {
        // Clear existing options except the first one
        while (presetSelect.options.length > 1) {
            presetSelect.remove(1);
        }

        // Add options for each preset
        crosspointPresets.forEach(preset => {
            const option = document.createElement('option');
            option.value = preset.id;
            option.textContent = preset.name;
            presetSelect.appendChild(option);
        });
    }

    // Update preset buttons based on selection
    function updatePresetButtons() {
        const isPresetSelected = presetSelect.value !== '';
        recallPresetBtn.disabled = !isPresetSelected;
        deletePresetBtn.disabled = !isPresetSelected;
    }

    // Save current crosspoint state as a preset
    function saveCurrentCrosspointState() {
        const presetName = presetNameInput.value.trim();
        if (!presetName) {
            showToast('Please enter a preset name', 'error');
            return;
        }

        // Show progress indicator
        showPresetProgress();
        updatePresetProgress(0, 1);

        // Create a new preset object
        const newPreset = {
            id: Date.now().toString(),
            name: presetName,
            timestamp: Date.now(),
            protocol: protocolConfig.type,
            crosspoints: {},
            locks: {}
        };

        // Query all outputs to get current state
        const maxOutputs = routerConfig.outputs || 384;
        const batchSize = 10; // Number of outputs to query at once
        let queriedOutputs = 0;

        // Set total for progress tracking
        currentPresetProgress.total = maxOutputs;
        currentPresetProgress.completed = 0;

        // Function to query outputs in batches
        function queryNextBatch(startOutput) {
            if (startOutput > maxOutputs) {
                // All outputs queried, save the preset
                finalizeSavePreset(newPreset);
                return;
            }

            // Query a batch of outputs
            const endOutput = Math.min(startOutput + batchSize - 1, maxOutputs);
            const outputs = Array.from({length: endOutput - startOutput + 1}, (_, i) => startOutput + i);

            // Create promises for each output query
            const promises = outputs.map(output => {
                return new Promise(resolve => {
                    // Get the query command for the current protocol
                    const queryCmd = getProtocolCommand('queryStatus', output);

                    // Send the command
                    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
                        // Create a one-time event listener for this specific output
                        const messageHandler = function(event) {
                            try {
                                const data = JSON.parse(event.data);
                                if (data.type === 'routerResponse') {
                                    const message = data.message;

                                    // Parse the response based on protocol
                                    if (protocolConfig.type === 'quartz') {
                                        // Quartz response format: .A{output},{source}
                                        const match = message.match(/\.A(\d+),(\d+)/);
                                        if (match && parseInt(match[1]) === output) {
                                            newPreset.crosspoints[output] = parseInt(match[2]);
                                            wsConnection.removeEventListener('message', messageHandler);

                                            // Check if output is locked
                                            const lockQueryCmd = getProtocolCommand('queryLock', output);
                                            sendRouterCommand(lockQueryCmd);

                                            // Increment completed count for progress
                                            currentPresetProgress.completed++;
                                            updatePresetProgress(currentPresetProgress.completed, currentPresetProgress.total);

                                            resolve();
                                        }
                                    } else if (protocolConfig.type === 'rcp3') {
                                        // RCP3 response format varies, but we're looking for matrix status
                                        if (message.includes('RCP3_MATRIX_RESPONSE')) {
                                            const parts = message.split(':')[1].split(',');
                                            if (parts.length >= 2 && parseInt(parts[0]) === output) {
                                                newPreset.crosspoints[output] = parseInt(parts[1]);
                                                wsConnection.removeEventListener('message', messageHandler);

                                                // Check if output is locked
                                                const lockQueryCmd = getProtocolCommand('queryLock', output);
                                                sendRouterCommand(lockQueryCmd);

                                                // Increment completed count for progress
                                                currentPresetProgress.completed++;
                                                updatePresetProgress(currentPresetProgress.completed, currentPresetProgress.total);

                                                resolve();
                                            }
                                        }
                                    }
                                }
                            } catch (error) {
                                console.error('Error processing message:', error);
                            }
                        };

                        // Add the event listener
                        wsConnection.addEventListener('message', messageHandler);

                        // Send the query command
                        sendRouterCommand(queryCmd);

                        // Set a timeout to resolve the promise after 2 seconds if no response
                        setTimeout(() => {
                            wsConnection.removeEventListener('message', messageHandler);
                            currentPresetProgress.completed++;
                            updatePresetProgress(currentPresetProgress.completed, currentPresetProgress.total);
                            resolve();
                        }, 2000);
                    } else {
                        // WebSocket not connected, resolve immediately
                        currentPresetProgress.completed++;
                        updatePresetProgress(currentPresetProgress.completed, currentPresetProgress.total);
                        resolve();
                    }
                });
            });

            // When all promises in this batch are resolved, query the next batch
            Promise.all(promises).then(() => {
                queryNextBatch(endOutput + 1);
            });
        }

        // Start querying outputs
        queryNextBatch(1);
    }

    // Finalize saving the preset after all queries are complete
    function finalizeSavePreset(preset) {
        // Add the preset to the list
        crosspointPresets.push(preset);

        // Save to localStorage
        saveCrosspointPresets();

        // Update the UI
        populatePresetSelect();
        presetSelect.value = preset.id;
        presetNameInput.value = '';
        updatePresetButtons();

        // Hide progress indicator
        hidePresetProgress();

        // Show success message
        showToast(`Preset "${preset.name}" saved successfully`, 'success');
    }

    // Delete the selected preset
    function deleteSelectedPreset() {
        const presetId = presetSelect.value;
        if (!presetId) return;

        // Find the preset index
        const presetIndex = crosspointPresets.findIndex(p => p.id === presetId);
        if (presetIndex === -1) return;

        // Get the preset name for the toast message
        const presetName = crosspointPresets[presetIndex].name;

        // Remove the preset
        crosspointPresets.splice(presetIndex, 1);

        // Save to localStorage
        saveCrosspointPresets();

        // Update the UI
        populatePresetSelect();
        presetSelect.selectedIndex = 0;
        updatePresetButtons();

        // Show success message
        showToast(`Preset "${presetName}" deleted`, 'success');
    }

    // Recall the selected preset
    function recallSelectedPreset() {
        const presetId = presetSelect.value;
        if (!presetId) {
            showToast('Please select a preset to recall', 'error');
            return;
        }

        // Find the preset
        const preset = crosspointPresets.find(p => p.id === presetId);
        if (!preset) {
            showToast('Selected preset not found', 'error');
            return;
        }

        // Show progress indicator
        showPresetProgress();

        // Get the total number of crosspoints to recall
        const totalCrosspoints = Object.keys(preset.crosspoints).length;
        currentPresetProgress.total = totalCrosspoints;
        currentPresetProgress.completed = 0;
        updatePresetProgress(0, totalCrosspoints);

        // Recall crosspoints in batches
        const batchSize = 5; // Number of takes to send at once
        const destinations = Object.keys(preset.crosspoints).map(Number);
        let currentIndex = 0;

        function recallNextBatch() {
            if (currentIndex >= destinations.length) {
                // All crosspoints recalled
                hidePresetProgress();
                showToast(`Preset "${preset.name}" recalled successfully`, 'success');
                return;
            }

            // Get the next batch of destinations
            const endIndex = Math.min(currentIndex + batchSize, destinations.length);
            const batch = destinations.slice(currentIndex, endIndex);

            // Create promises for each take command
            const promises = batch.map(destination => {
                return new Promise(resolve => {
                    const source = preset.crosspoints[destination];
                    const takeCmd = getProtocolCommand('take', destination, source);

                    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
                        sendRouterCommand(takeCmd);

                        // Increment completed count for progress
                        currentPresetProgress.completed++;
                        updatePresetProgress(currentPresetProgress.completed, currentPresetProgress.total);

                        // Add a small delay to avoid overwhelming the router
                        setTimeout(resolve, 100);
                    } else {
                        // WebSocket not connected, resolve immediately
                        currentPresetProgress.completed++;
                        updatePresetProgress(currentPresetProgress.completed, currentPresetProgress.total);
                        resolve();
                    }
                });
            });

            // When all promises in this batch are resolved, recall the next batch
            Promise.all(promises).then(() => {
                currentIndex = endIndex;
                setTimeout(recallNextBatch, 200); // Add a delay between batches
            });
        }

        // Start recalling crosspoints
        recallNextBatch();
    }

    // Export crosspoint presets to a file
    function exportCrosspointPresets() {
        if (crosspointPresets.length === 0) {
            showToast('No presets to export', 'error');
            return;
        }

        const blob = new Blob([JSON.stringify(crosspointPresets, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'router-crosspoint-presets.json';
        document.body.appendChild(a);
        a.click();
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 0);

        showToast('Presets exported successfully', 'success');
    }

    // Import crosspoint presets from a file
    function importCrosspointPresetsFromFile(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const importedPresets = JSON.parse(e.target.result);
                if (Array.isArray(importedPresets)) {
                    // Validate each preset
                    const validPresets = importedPresets.filter(preset => {
                        return preset && preset.id && preset.name && preset.crosspoints;
                    });

                    if (validPresets.length > 0) {
                        // Add the imported presets to the existing ones
                        crosspointPresets = [...crosspointPresets, ...validPresets];

                        // Save to localStorage
                        saveCrosspointPresets();

                        // Update the UI
                        populatePresetSelect();
                        updatePresetButtons();

                        showToast(`Imported ${validPresets.length} presets successfully`, 'success');
                    } else {
                        showToast('No valid presets found in the file', 'error');
                    }
                } else {
                    showToast('Invalid file format. Expected an array of presets.', 'error');
                }
            } catch (err) {
                showToast('Failed to import presets: ' + err.message, 'error');
            }
        };
        reader.readAsText(file);
    }

    // Show the preset progress indicator
    function showPresetProgress() {
        presetProgress.style.display = 'block';
    }

    // Hide the preset progress indicator
    function hidePresetProgress() {
        presetProgress.style.display = 'none';
    }

    // Update the preset progress indicator
    function updatePresetProgress(completed, total) {
        const percentage = Math.round((completed / total) * 100);
        presetProgressBar.style.width = `${percentage}%`;
        presetProgressText.textContent = `${percentage}% (${completed}/${total})`;
    }

    function initEventListeners() {
        destinationSelect.addEventListener('change', () => {
            if (destinationSelect.value === '') return;

            selectedOutput = parseInt(destinationSelect.value);
            updateSelectedOutputName();
            queryOutputState(selectedOutput);
            highlightSelectedDestinationButton(selectedOutput);
        });

        sourceSelect.addEventListener('change', () => {
            if (sourceSelect.value === '') return;

            selectedInput = parseInt(sourceSelect.value);
            updateTakeButton();
            highlightSelectedSourceButton(selectedInput);
        });

        takeBtn.addEventListener('click', performTake);
        lockBtn.addEventListener('click', toggleLock);
        refreshBtn.addEventListener('click', queryRouterState);
        connectBtn.addEventListener('click', connectToRouterManually);
        disconnectBtn.addEventListener('click', disconnectFromRouterManually);

        addDestinationCategoryBtn.addEventListener('click', () => showCategoryForm('destination'));
        addSourceCategoryBtn.addEventListener('click', () => showCategoryForm('source'));
        saveCategoryBtn.addEventListener('click', saveCategoryHandler);
        cancelCategoryBtn.addEventListener('click', () => {
            document.querySelector('.category-form').style.display = 'none';
        });

        // Add event listeners for Save/Load UI buttons
        document.getElementById('save-ui-btn').addEventListener('click', saveUIState);
        document.getElementById('load-ui-btn').addEventListener('click', loadUIState);

        // Add event listeners for Save All/Load All buttons
        document.getElementById('save-all-btn').addEventListener('click', saveAllState);
        document.getElementById('load-all-btn').addEventListener('click', loadAllState);

        // Add event listeners for Export/Import All buttons
        document.getElementById('export-all-btn').addEventListener('click', exportAllState);
        document.getElementById('import-all-btn').addEventListener('click', function() {
            document.getElementById('import-all-file').click();
        });
        document.getElementById('import-all-file').addEventListener('change', function(e) {
            if (e.target.files && e.target.files[0]) {
                importAllStateFromFile(e.target.files[0]);
            }
        });
    }

    // Add this function to show toast notifications
    function showToast(message, type = 'info') {
        // Create toast container if it doesn't exist
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast ${type} show`;
        toast.textContent = message;

        // Add to container
        toastContainer.appendChild(toast);

        // Remove after 3 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 3000);
    }

    // --- Monitor Wall Tab Logic ---
    function initMonitorWall() {
        loadWallWindows();
        updateWallLockButton();
        renderWallSourceCategories();
        renderWallSourceButtons();
        wallTakeBtn.disabled = true;

        createWallWindowBtn.addEventListener('click', createNewWallWindow);
        lockWallBtn.addEventListener('click', toggleWallLock);
        wallWindowWidthSlider.addEventListener('input', function() {
            wallWindowWidthValue.textContent = `${this.value}px`;
            if (selectedWallWindow) {
                selectedWallWindow.width = parseInt(this.value);
                updateWallWindowDOM(selectedWallWindow);
                saveWallWindows();
            }
        });
        wallWindowHeightSlider.addEventListener('input', function() {
            wallWindowHeightValue.textContent = `${this.value}px`;
            if (selectedWallWindow) {
                selectedWallWindow.height = parseInt(this.value);
                updateWallWindowDOM(selectedWallWindow);
                saveWallWindows();
            }
        });
        wallTakeBtn.addEventListener('click', performWallTake);
        wallSourceSelect.addEventListener('change', function() {
            selectedWallSource = parseInt(this.value);
            updateWallTakeButton();
            highlightSelectedWallSourceButton(selectedWallSource);
        });
        // --- NEW: re-render source buttons if confirm checkbox changes ---
        if (wallTakeConfirmCheckbox) {
            wallTakeConfirmCheckbox.addEventListener('change', () => {
                renderWallSourceButtons();
            });
        }
    }

    function createNewWallWindow() {
        const id = 'wall-window-' + Date.now();
        const width = parseInt(wallWindowWidthSlider.value) || 200;
        const height = parseInt(wallWindowHeightSlider.value) || 150;
        const windowInfo = {
            id,
            x: 20,
            y: 20,
            width,
            height,
            name: 'Window ' + (wallWindows.length + 1),
            destination: null // router output assignment
        };
        wallWindows.push(windowInfo);
        renderWallWindow(windowInfo);
        selectWallWindow(windowInfo);
        saveWallWindows();
    }

    function renderWallWindow(windowInfo) {
        let win = document.getElementById(windowInfo.id);
        if (!win) {
            win = document.createElement('div');
            win.id = windowInfo.id;
            win.className = 'monitor-button';
            win.style.position = 'absolute';
            monitorWallBoard.appendChild(win);
        }
        win.style.left = windowInfo.x + 'px';
        win.style.top = windowInfo.y + 'px';
        win.style.width = windowInfo.width + 'px';
        win.style.height = windowInfo.height + 'px';
        win.innerHTML = '';
        // Label
        const label = document.createElement('div');
        label.className = 'monitor-button-label';
        label.textContent = windowInfo.name;
        win.appendChild(label);
        // Assignment (output)
        const assignment = document.createElement('div');
        assignment.className = 'monitor-button-assignment';
        if (windowInfo.destination) {
            let destName = destinationNames[windowInfo.destination] ? `Output ${windowInfo.destination}: ${destinationNames[windowInfo.destination]}` : `Output ${windowInfo.destination}`;
            // Show routed input status if available
            let routedInput = null;
            if (outputStates[windowInfo.destination] && outputStates[windowInfo.destination].source !== undefined) {
                const srcNum = outputStates[windowInfo.destination].source;
                routedInput = sourceNames[srcNum] ? `Input ${srcNum} (${sourceNames[srcNum]})` : `Input ${srcNum}`;
            }
            assignment.innerHTML = destName + (routedInput ? `<br><span style='color:#007bff'>Routed: ${routedInput}</span>` : '<br><span style="color:#888">Routed: ?</span>');
        } else {
            assignment.textContent = 'Not assigned';
        }
        win.appendChild(assignment);
        // Controls
        const controls = document.createElement('div');
        controls.className = 'monitor-button-controls';
        // Rename
        const renameBtn = document.createElement('div');
        renameBtn.className = 'monitor-button-rename';
        renameBtn.innerHTML = '✏️';
        renameBtn.title = 'Rename/Assign';
        renameBtn.onclick = (e) => {
            e.stopPropagation();
            showWallWindowModal(windowInfo);
        };
        controls.appendChild(renameBtn);
        // Remove
        const removeBtn = document.createElement('div');
        removeBtn.className = 'monitor-button-remove';
        removeBtn.innerHTML = '×';
        removeBtn.title = 'Delete';
        removeBtn.onclick = (e) => {
            e.stopPropagation();
            removeWallWindow(windowInfo.id);
        };
        controls.appendChild(removeBtn);
        win.appendChild(controls);
        // Select
        win.onclick = () => selectWallWindow(windowInfo);
        // Draggable
        makeWallWindowDraggable(win, windowInfo);
        // Resizable
        makeWallWindowResizable(win, windowInfo);
        // Locked visual
        if (wallWindowsLocked) win.classList.add('locked-draggable');
        else win.classList.remove('locked-draggable');
        // Selected visual
        if (selectedWallWindow && selectedWallWindow.id === windowInfo.id) {
            win.classList.add('selected');
            win.style.backgroundColor = '#28a745'; // green
            win.style.borderColor = '#28a745';
            win.style.boxShadow = '0 0 8px rgba(40, 167, 69, 0.5)';
        } else {
            win.classList.remove('selected');
            win.style.backgroundColor = '#f8f9fa'; // light grey/white
            win.style.borderColor = '#ffc107';
            win.style.boxShadow = '0 0 8px rgba(255, 193, 7, 0.2)';
        }
    }

    function updateWallWindowDOM(windowInfo) {
        renderWallWindow(windowInfo);
    }

    function selectWallWindow(windowInfo) {
        selectedWallWindow = windowInfo;
        // Update sliders
        wallWindowWidthSlider.value = windowInfo.width;
        wallWindowWidthValue.textContent = windowInfo.width + 'px';
        wallWindowHeightSlider.value = windowInfo.height;
        wallWindowHeightValue.textContent = windowInfo.height + 'px';
        // Highlight
        wallWindows.forEach(w => updateWallWindowDOM(w));
        updateWallTakeButton();
    }

    function removeWallWindow(id) {
        wallWindows = wallWindows.filter(w => w.id !== id);
        const win = document.getElementById(id);
        if (win) win.remove();
        if (selectedWallWindow && selectedWallWindow.id === id) selectedWallWindow = null;
        saveWallWindows();
    }

    function makeWallWindowDraggable(element, windowInfo) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
        element.onmousedown = dragMouseDown;
        function dragMouseDown(e) {
            if (wallWindowsLocked) return;
            if (element.querySelector('.monitor-button-controls').contains(e.target)) return;
            pos3 = e.clientX;
            pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            document.onmousemove = elementDrag;
        }
        function elementDrag(e) {
            e = e || window.event;
            e.preventDefault();
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            let newTop = element.offsetTop - pos2;
            let newLeft = element.offsetLeft - pos1;
            const boardRect = monitorWallBoard.getBoundingClientRect();
            const elementRect = element.getBoundingClientRect();
            newTop = Math.max(0, Math.min(newTop, monitorWallBoard.scrollHeight - elementRect.height));
            newLeft = Math.max(0, Math.min(newLeft, monitorWallBoard.scrollWidth - elementRect.width));
            element.style.top = newTop + 'px';
            element.style.left = newLeft + 'px';
        }
        function closeDragElement() {
            document.onmouseup = null;
            document.onmousemove = null;
            if (windowInfo) {
                windowInfo.x = Math.round(parseFloat(element.style.left));
                windowInfo.y = Math.round(parseFloat(element.style.top));
                saveWallWindows();
            }
        }
    }

    function makeWallWindowResizable(win, windowInfo) {
        // Add a resize handle if not present
        if (!win.querySelector('.wall-resize-handle')) {
            const handle = document.createElement('div');
            handle.className = 'wall-resize-handle';
            win.appendChild(handle);
            let isResizing = false;
            let startX, startY, startW, startH;
            handle.addEventListener('mousedown', function(e) {
                e.stopPropagation();
                isResizing = true;
                startX = e.clientX;
                startY = e.clientY;
                startW = win.offsetWidth;
                startH = win.offsetHeight;
                document.body.style.cursor = 'nwse-resize';
                document.onmousemove = function(e2) {
                    if (!isResizing) return;
                    let newW = Math.max(50, startW + (e2.clientX - startX));
                    let newH = Math.max(50, startH + (e2.clientY - startY));
                    win.style.width = newW + 'px';
                    win.style.height = newH + 'px';
                    windowInfo.width = newW;
                    windowInfo.height = newH;
                };
                document.onmouseup = function() {
                    isResizing = false;
                    document.body.style.cursor = '';
                    document.onmousemove = null;
                    document.onmouseup = null;
                    saveWallWindows();
                };
            });
        }
    }

    function showWallWindowModal(windowInfo) {
        // Simple prompt for name and destination assignment
        const newName = prompt('Enter window name:', windowInfo.name);
        if (newName !== null) windowInfo.name = newName;
        let dest = prompt('Assign router output (number):', windowInfo.destination || '');
        if (dest !== null && dest !== '') windowInfo.destination = parseInt(dest);
        updateWallWindowDOM(windowInfo);
        saveWallWindows();
        updateWallTakeButton();
    }

    function toggleWallLock() {
        wallWindowsLocked = !wallWindowsLocked;
        updateWallLockButton();
        wallWindows.forEach(w => updateWallWindowDOM(w));
        saveWallWindows();
    }
    function updateWallLockButton() {
        lockWallBtn.textContent = wallWindowsLocked ? 'Unlock Windows' : 'Lock Windows';
        if (wallWindowsLocked) lockWallBtn.classList.add('active');
        else lockWallBtn.classList.remove('active');
    }
    function saveWallWindows() {
        localStorage.setItem('monitorWallWindows', JSON.stringify(wallWindows));
    }
    function loadWallWindows() {
        const saved = localStorage.getItem('monitorWallWindows');
        if (saved) {
            wallWindows = JSON.parse(saved);
            monitorWallBoard.innerHTML = '';
            wallWindows.forEach(w => renderWallWindow(w));
            refreshAllWallWindowInputs();
        }
    }

    // --- Monitor Wall Copy/Paste Size Logic ---
    let copiedWallWindowSize = null;

    const copyWallSizeBtn = document.getElementById('copy-wall-size-btn');
    const pasteWallSizeBtn = document.getElementById('paste-wall-size-btn');

    copyWallSizeBtn.addEventListener('click', () => {
        if (selectedWallWindow) {
            copiedWallWindowSize = {
                width: selectedWallWindow.width,
                height: selectedWallWindow.height
            };
            showToast('Window size copied', 'success');
        } else {
            showToast('Select a window to copy size', 'error');
        }
    });

    pasteWallSizeBtn.addEventListener('click', () => {
        if (selectedWallWindow && copiedWallWindowSize) {
            selectedWallWindow.width = copiedWallWindowSize.width;
            selectedWallWindow.height = copiedWallWindowSize.height;
            updateWallWindowDOM(selectedWallWindow);
            saveWallWindows();
            showToast('Window size pasted', 'success');
        } else {
            showToast('Select a window and copy a size first', 'error');
        }
    });

    // --- Wall Sources/Categories Logic ---
    function renderWallSourceCategories() {
        wallSourceCategories.innerHTML = '';
        customCategories.source.forEach(category => {
            const btn = document.createElement('button');
            btn.className = 'category-button';
            btn.textContent = category.name;
            btn.dataset.category = category.id;
            btn.onclick = () => {
                selectedWallCategory = category.id;
                renderWallSourceCategories();
                renderWallSourceButtons();
            };
            if (category.id === selectedWallCategory) btn.classList.add('active');
            wallSourceCategories.appendChild(btn);
        });
    }
    function renderWallSourceButtons() {
        wallSourceButtons.innerHTML = '';
        let inputs;
        if (selectedWallCategory === 'ALL') {
            inputs = allSourceRange;
        } else {
            const category = customCategories.source.find(cat => cat.id === selectedWallCategory);
            inputs = category ? category.ios : allSourceRange;
        }
        inputs.forEach(sourceNum => {
            const btn = document.createElement('div');
            btn.className = 'square-button input';
            btn.dataset.input = sourceNum;
            let name = sourceNames[sourceNum] || `IN ${sourceNum}`;
            btn.innerHTML = `<span class="button-text">${formatButtonText(name)}</span>`;
            if (sourceNum === selectedWallSource) btn.classList.add('selected');
            btn.onclick = () => {
                selectedWallSource = sourceNum;
                wallSourceSelect.value = sourceNum;
                renderWallSourceButtons();
                updateWallTakeButton();
                // --- NEW LOGIC: Immediate route if confirm is unchecked ---
                if (wallTakeConfirmCheckbox && !wallTakeConfirmCheckbox.checked) {
                    // Only route if a window and destination are selected
                    if (selectedWallWindow && selectedWallWindow.destination) {
                        sendRouterCommand(`.SV${selectedWallWindow.destination},${selectedWallSource}`);
                        showToast(`Routed Input ${selectedWallSource} to Output ${selectedWallWindow.destination}`, 'success');
                        setTimeout(refreshAllWallWindowInputs, 400);
                    }
                }
            };
            wallSourceButtons.appendChild(btn);
        });
        // Populate select
        wallSourceSelect.innerHTML = '<option value="">-- Select Source --</option>';
        inputs.forEach(sourceNum => {
            const opt = document.createElement('option');
            opt.value = sourceNum;
            opt.textContent = sourceNames[sourceNum] ? `Input ${sourceNum}: ${sourceNames[sourceNum]}` : `Input ${sourceNum}`;
            wallSourceSelect.appendChild(opt);
        });
        if (selectedWallSource) wallSourceSelect.value = selectedWallSource;
    }
    function highlightSelectedWallSourceButton(sourceNum) {
        const buttons = wallSourceButtons.querySelectorAll('.square-button');
        buttons.forEach(btn => btn.classList.remove('selected'));
        const selectedBtn = wallSourceButtons.querySelector(`.square-button[data-input="${sourceNum}"]`);
        if (selectedBtn) selectedBtn.classList.add('selected');
    }
    function updateWallTakeButton() {
        wallTakeBtn.disabled = !(selectedWallSource && selectedWallWindow && selectedWallWindow.destination);
    }
    function performWallTake() {
        if (selectedWallSource && selectedWallWindow && selectedWallWindow.destination) {
            // Get the take command for the current protocol
            const takeCmd = getProtocolCommand('take', selectedWallWindow.destination, selectedWallSource);
            if (takeCmd) {
                sendRouterCommand(takeCmd);
                showToast(`Routed Input ${selectedWallSource} to Output ${selectedWallWindow.destination}`, 'success');
                setTimeout(refreshAllWallWindowInputs, 400); // Give router a moment to update
            }
        }
    }

    // Add CSS for resize handle
    (function addWallResizeHandleCSS() {
        const style = document.createElement('style');
        style.textContent = `
        .wall-resize-handle {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 16px;
            height: 16px;
            background: rgba(0,0,0,0.15);
            border-radius: 0 0 5px 0;
            cursor: nwse-resize;
            z-index: 10;
        }
        .wall-resize-handle:hover {
            background: #007bff;
        }
        `;
        document.head.appendChild(style);
    })();

    // --- Monitor Wall Board Resizing ---
    (function enableMonitorWallBoardResizing() {
        const board = document.getElementById('monitor-wall-board');
        const handle = document.getElementById('wall-board-resize-handle');
        // Initial size from localStorage or default
        let width = parseInt(localStorage.getItem('monitorWallBoardWidth')) || 1200;
        let height = parseInt(localStorage.getItem('monitorWallBoardHeight')) || 800;
        board.style.width = width + 'px';
        board.style.height = height + 'px';
        board.style.minWidth = '400px';
        board.style.minHeight = '300px';
        board.style.maxWidth = '100vw';
        board.style.maxHeight = '90vh';
        board.style.position = 'relative';
        board.style.overflow = 'auto';
        handle.style.position = 'absolute';
        handle.style.right = 0;
        handle.style.bottom = 0;
        handle.style.width = '24px';
        handle.style.height = '24px';
        handle.style.background = 'rgba(0,0,0,0.15)';
        handle.style.borderRadius = '0 0 5px 0';
        handle.style.cursor = 'nwse-resize';
        handle.style.zIndex = 1000;
        handle.style.display = 'block';
        // Drag logic
        let resizing = false;
        let startX, startY, startW, startH;
        handle.addEventListener('mousedown', function(e) {
            e.preventDefault();
            resizing = true;
            startX = e.clientX;
            startY = e.clientY;
            startW = board.offsetWidth;
            startH = board.offsetHeight;
            document.body.style.cursor = 'nwse-resize';
        });
        document.addEventListener('mousemove', function(e) {
            if (!resizing) return;
            let newW = Math.max(400, Math.min(window.innerWidth - 100, startW + (e.clientX - startX)));
            let newH = Math.max(300, Math.min(window.innerHeight - 100, startH + (e.clientY - startY)));
            board.style.width = newW + 'px';
            board.style.height = newH + 'px';
        });
        document.addEventListener('mouseup', function() {
            if (resizing) {
                resizing = false;
                document.body.style.cursor = '';
                // Save size
                localStorage.setItem('monitorWallBoardWidth', board.offsetWidth);
                localStorage.setItem('monitorWallBoardHeight', board.offsetHeight);
            }
        });
    })();

    // --- Monitor Wall Grid Size Logic ---
    function updateMonitorWallBoardSize() {
        const cols = parseInt(wallColsInput.value) || 1;
        const rows = parseInt(wallRowsInput.value) || 1;
        const width = cols * DEFAULT_CELL_SIZE;
        const height = rows * DEFAULT_CELL_SIZE;
        monitorWallBoard.style.width = width + 'px';
        monitorWallBoard.style.height = height + 'px';
        localStorage.setItem('monitorWallCols', cols);
        localStorage.setItem('monitorWallRows', rows);
    }

    wallColsInput.addEventListener('input', updateMonitorWallBoardSize);
    wallRowsInput.addEventListener('input', updateMonitorWallBoardSize);

    (function restoreMonitorWallGridSize() {
        const savedCols = parseInt(localStorage.getItem('monitorWallCols')) || 6;
        const savedRows = parseInt(localStorage.getItem('monitorWallRows')) || 4;
        wallColsInput.value = savedCols;
        wallRowsInput.value = savedRows;
        updateMonitorWallBoardSize();
    })();

    // Helper to refresh routed input for all wall windows
    function refreshAllWallWindowInputs() {
        wallWindows.forEach(w => {
            if (w.destination) {
                const queryStatusCmd = getProtocolCommand('queryStatus', w.destination);
                if (queryStatusCmd) sendRouterCommand(queryStatusCmd);
            }
        });
    }

    // --- UI Save/Load ---
    function saveUIState() {
        localStorage.setItem('routerUIState', JSON.stringify({
            monitorButtons,
            customCategories,
            wallWindows
        }));
        showToast('UI state saved!', 'success');
    }
    function loadUIState() {
        const state = JSON.parse(localStorage.getItem('routerUIState'));
        if (state) {
            monitorButtons = state.monitorButtons || [];
            customCategories = state.customCategories || {destination: [], source: []};
            wallWindows = state.wallWindows || [];
            // Re-render UI
            document.getElementById('monitor-board').innerHTML = '';
            monitorButtons.forEach(renderMonitorButton);
            updateCategoryButtons();
            renderCategoryLists();
            renderWallSourceCategories();
            renderWallSourceButtons();
            document.getElementById('monitor-wall-board').innerHTML = '';
            wallWindows.forEach(renderWallWindow);
            showToast('UI state loaded!', 'success');
        } else {
            showToast('No saved UI state found.', 'error');
        }
    }

    // --- Save/Load All ---
    function saveAllState() {
        localStorage.setItem('routerAllState', JSON.stringify({
            customCategories,
            wallWindows
        }));
        showToast('All state saved!', 'success');
    }
    function loadAllState() {
        const state = JSON.parse(localStorage.getItem('routerAllState'));
        if (state) {
            customCategories = state.customCategories || {destination: [], source: []};
            wallWindows = state.wallWindows || [];
            // Re-render UI
            updateCategoryButtons();
            renderCategoryLists();
            renderWallSourceCategories();
            renderWallSourceButtons();
            document.getElementById('monitor-wall-board').innerHTML = '';
            wallWindows.forEach(renderWallWindow);
            showToast('All state loaded!', 'success');
        } else {
            showToast('No saved state found.', 'error');
        }
    }

    // --- Export/Import All ---
    function exportAllState() {
        const state = {
            customCategories,
            wallWindows
        };
        const blob = new Blob([JSON.stringify(state, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'router-ui-state.json';
        document.body.appendChild(a);
        a.click();
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 0);
    }
    function importAllStateFromFile(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const state = JSON.parse(e.target.result);
                if (state && state.customCategories && state.wallWindows) {
                    customCategories = state.customCategories;
                    wallWindows = state.wallWindows;
                    updateCategoryButtons();
                    renderCategoryLists();
                    renderWallSourceCategories();
                    renderWallSourceButtons();
                    document.getElementById('monitor-wall-board').innerHTML = '';
                    wallWindows.forEach(renderWallWindow);
                    showToast('All state imported!', 'success');
                } else {
                    showToast('Invalid file format.', 'error');
                }
            } catch (err) {
                showToast('Failed to import: ' + err.message, 'error');
            }
        };
        reader.readAsText(file);
    }

    function init() {
        ensureBasicCategories();
        initTabNavigation();
        initDestinationCategories();
        initSourceCategories();
        updateCategoryButtons();
        renderCategoryLists();
        initMonitors();
        initEventListeners();
        initMonitorWall();
        initSetupTab();
        initNamesTab();
        connectWebSocket();

        // If app settings specify a default tab other than router-control, switch to it
        if (appSettings.defaultTab && appSettings.defaultTab !== 'router-control') {
            const tabButton = document.querySelector(`.tab-button[data-tab="${appSettings.defaultTab}"]`);
            if (tabButton) {
                tabButton.click();
            }
        }

        // Update the router address display in the Router Control tab
        const routerAddressDisplay = document.querySelector('.status-bar > div > span');
        if (routerAddressDisplay) {
            routerAddressDisplay.textContent = `Router: ${routerConfig.host}:${routerConfig.port}`;
        }
    }

    init();
});
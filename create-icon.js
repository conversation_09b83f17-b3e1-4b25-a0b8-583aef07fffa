const fs = require('fs');
const path = require('path');

// Create a simple 256x256 PNG with a router icon
const svgContent = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" width="256" height="256">
  <rect width="256" height="256" fill="#4a86e8" rx="30" ry="30"/>
  <rect x="50" y="80" width="156" height="50" rx="5" ry="5" fill="#ffffff"/>
  <circle cx="70" cy="105" r="10" fill="#00ff00"/>
  <circle cx="100" cy="105" r="10" fill="#00ff00"/>
  <circle cx="130" cy="105" r="10" fill="#ff0000"/>
  <circle cx="160" cy="105" r="10" fill="#ff0000"/>
  <circle cx="190" cy="105" r="10" fill="#ffff00"/>
  <rect x="50" y="140" width="156" height="36" rx="5" ry="5" fill="#ffffff"/>
  <text x="128" y="164" font-family="Arial" font-size="16" text-anchor="middle" fill="#000000">ROUTER CONTROL</text>
</svg>`;

// Create public directory if it doesn't exist
const publicDir = path.join(__dirname, 'public');
if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir);
}

// Save SVG file
fs.writeFileSync(path.join(publicDir, 'icon.svg'), svgContent);
console.log('Created icon.svg in public directory');

// Since we need an .ico file as well, but creating true .ico files is complex,
// we'll use the svg for now and adjust the package.json to use it
console.log('Please update package.json to use svg instead of ico if needed');
console.log('Icon creation complete!');